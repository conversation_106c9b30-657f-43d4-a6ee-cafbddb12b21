[{"name": "Process Start", "start": 1748253133320, "end": 1748253136176, "duration": 2856, "pid": 28812, "index": 0}, {"name": "Application Start", "start": 1748253136179, "end": 1748253140179, "duration": 4000, "pid": 28812, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748253136232, "end": 1748253136362, "duration": 130, "pid": 28812, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748253136363, "end": 1748253136476, "duration": 113, "pid": 28812, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748253136364, "end": 1748253136367, "duration": 3, "pid": 28812, "index": 4}, {"name": "Require(1) config/config.prod.js", "start": 1748253136372, "end": 1748253136373, "duration": 1, "pid": 28812, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748253136375, "end": 1748253136375, "duration": 0, "pid": 28812, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748253136376, "end": 1748253136377, "duration": 1, "pid": 28812, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748253136379, "end": 1748253136380, "duration": 1, "pid": 28812, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748253136382, "end": 1748253136383, "duration": 1, "pid": 28812, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748253136384, "end": 1748253136391, "duration": 7, "pid": 28812, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748253136392, "end": 1748253136393, "duration": 1, "pid": 28812, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748253136394, "end": 1748253136395, "duration": 1, "pid": 28812, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748253136396, "end": 1748253136397, "duration": 1, "pid": 28812, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1748253136398, "end": 1748253136398, "duration": 0, "pid": 28812, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1748253136400, "end": 1748253136400, "duration": 0, "pid": 28812, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1748253136402, "end": 1748253136403, "duration": 1, "pid": 28812, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1748253136404, "end": 1748253136405, "duration": 1, "pid": 28812, "index": 17}, {"name": "Require(14) node_modules/egg-jwt/config/config.default.js", "start": 1748253136406, "end": 1748253136407, "duration": 1, "pid": 28812, "index": 18}, {"name": "Require(15) node_modules/egg-cors/config/config.default.js", "start": 1748253136408, "end": 1748253136408, "duration": 0, "pid": 28812, "index": 19}, {"name": "Require(16) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748253136409, "end": 1748253136410, "duration": 1, "pid": 28812, "index": 20}, {"name": "Require(17) node_modules/egg-mysql/config/config.default.js", "start": 1748253136413, "end": 1748253136414, "duration": 1, "pid": 28812, "index": 21}, {"name": "Require(18) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748253136415, "end": 1748253136416, "duration": 1, "pid": 28812, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748253136417, "end": 1748253136418, "duration": 1, "pid": 28812, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748253136420, "end": 1748253136420, "duration": 0, "pid": 28812, "index": 24}, {"name": "Require(21) node_modules/egg-static/config/config.prod.js", "start": 1748253136447, "end": 1748253136467, "duration": 20, "pid": 28812, "index": 25}, {"name": "Require(22) config/config.prod.js", "start": 1748253136476, "end": 1748253136476, "duration": 0, "pid": 28812, "index": 26}, {"name": "Load extend/application.js", "start": 1748253136479, "end": 1748253136697, "duration": 218, "pid": 28812, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1748253136480, "end": 1748253136481, "duration": 1, "pid": 28812, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1748253136482, "end": 1748253136486, "duration": 4, "pid": 28812, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1748253136489, "end": 1748253136504, "duration": 15, "pid": 28812, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1748253136518, "end": 1748253136532, "duration": 14, "pid": 28812, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1748253136534, "end": 1748253136539, "duration": 5, "pid": 28812, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1748253136540, "end": 1748253136543, "duration": 3, "pid": 28812, "index": 33}, {"name": "Require(29) node_modules/egg-jwt/app/extend/application.js", "start": 1748253136544, "end": 1748253136684, "duration": 140, "pid": 28812, "index": 34}, {"name": "Load extend/request.js", "start": 1748253136697, "end": 1748253136729, "duration": 32, "pid": 28812, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1748253136712, "end": 1748253136716, "duration": 4, "pid": 28812, "index": 36}, {"name": "Load extend/response.js", "start": 1748253136729, "end": 1748253136758, "duration": 29, "pid": 28812, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1748253136739, "end": 1748253136747, "duration": 8, "pid": 28812, "index": 38}, {"name": "Load extend/context.js", "start": 1748253136758, "end": 1748253136895, "duration": 137, "pid": 28812, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1748253136759, "end": 1748253136815, "duration": 56, "pid": 28812, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1748253136816, "end": 1748253136822, "duration": 6, "pid": 28812, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1748253136825, "end": 1748253136825, "duration": 0, "pid": 28812, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1748253136827, "end": 1748253136872, "duration": 45, "pid": 28812, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1748253136873, "end": 1748253136875, "duration": 2, "pid": 28812, "index": 44}, {"name": "Require(37) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748253136877, "end": 1748253136878, "duration": 1, "pid": 28812, "index": 45}, {"name": "Require(38) node_modules/egg/app/extend/context.js", "start": 1748253136880, "end": 1748253136884, "duration": 4, "pid": 28812, "index": 46}, {"name": "Load extend/helper.js", "start": 1748253136895, "end": 1748253136985, "duration": 90, "pid": 28812, "index": 47}, {"name": "Require(39) node_modules/egg-security/app/extend/helper.js", "start": 1748253136897, "end": 1748253136940, "duration": 43, "pid": 28812, "index": 48}, {"name": "Require(40) node_modules/egg/app/extend/helper.js", "start": 1748253136951, "end": 1748253136953, "duration": 2, "pid": 28812, "index": 49}, {"name": "Require(41) app/extend/helper.js", "start": 1748253136956, "end": 1748253136958, "duration": 2, "pid": 28812, "index": 50}, {"name": "Load app.js", "start": 1748253136986, "end": 1748253137143, "duration": 157, "pid": 28812, "index": 51}, {"name": "Require(42) node_modules/egg-session/app.js", "start": 1748253136987, "end": 1748253136987, "duration": 0, "pid": 28812, "index": 52}, {"name": "Require(43) node_modules/egg-security/app.js", "start": 1748253136988, "end": 1748253136993, "duration": 5, "pid": 28812, "index": 53}, {"name": "Require(44) node_modules/egg-onerror/app.js", "start": 1748253136995, "end": 1748253137017, "duration": 22, "pid": 28812, "index": 54}, {"name": "Require(45) node_modules/egg-i18n/app.js", "start": 1748253137018, "end": 1748253137044, "duration": 26, "pid": 28812, "index": 55}, {"name": "Require(46) node_modules/egg-watcher/app.js", "start": 1748253137045, "end": 1748253137066, "duration": 21, "pid": 28812, "index": 56}, {"name": "Require(47) node_modules/egg-schedule/app.js", "start": 1748253137067, "end": 1748253137068, "duration": 1, "pid": 28812, "index": 57}, {"name": "Require(48) node_modules/egg-multipart/app.js", "start": 1748253137069, "end": 1748253137072, "duration": 3, "pid": 28812, "index": 58}, {"name": "Require(49) node_modules/egg-logrotator/app.js", "start": 1748253137073, "end": 1748253137073, "duration": 0, "pid": 28812, "index": 59}, {"name": "Require(50) node_modules/egg-static/app.js", "start": 1748253137074, "end": 1748253137074, "duration": 0, "pid": 28812, "index": 60}, {"name": "Require(51) node_modules/egg-sequelize/app.js", "start": 1748253137076, "end": 1748253137077, "duration": 1, "pid": 28812, "index": 61}, {"name": "Require(52) node_modules/egg-jwt/app.js", "start": 1748253137077, "end": 1748253137078, "duration": 1, "pid": 28812, "index": 62}, {"name": "Require(53) node_modules/egg-cors/app.js", "start": 1748253137078, "end": 1748253137079, "duration": 1, "pid": 28812, "index": 63}, {"name": "Require(54) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748253137080, "end": 1748253137088, "duration": 8, "pid": 28812, "index": 64}, {"name": "Require(55) node_modules/egg-mysql/app.js", "start": 1748253137089, "end": 1748253137133, "duration": 44, "pid": 28812, "index": 65}, {"name": "Require(56) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748253137133, "end": 1748253137140, "duration": 7, "pid": 28812, "index": 66}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748253137168, "end": 1748253140155, "duration": 2987, "pid": 28812, "index": 67}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748253138728, "end": 1748253138871, "duration": 143, "pid": 28812, "index": 68}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748253138771, "end": 1748253140080, "duration": 1309, "pid": 28812, "index": 69}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748253138950, "end": 1748253140178, "duration": 1228, "pid": 28812, "index": 70}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748253139147, "end": 1748253140157, "duration": 1010, "pid": 28812, "index": 71}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748253139360, "end": 1748253140113, "duration": 753, "pid": 28812, "index": 72}, {"name": "Load Service", "start": 1748253139361, "end": 1748253139648, "duration": 287, "pid": 28812, "index": 73}, {"name": "Load \"service\" to Context", "start": 1748253139361, "end": 1748253139648, "duration": 287, "pid": 28812, "index": 74}, {"name": "Load Middleware", "start": 1748253139649, "end": 1748253139934, "duration": 285, "pid": 28812, "index": 75}, {"name": "Load \"middlewares\" to Application", "start": 1748253139649, "end": 1748253139906, "duration": 257, "pid": 28812, "index": 76}, {"name": "Load Controller", "start": 1748253139934, "end": 1748253140003, "duration": 69, "pid": 28812, "index": 77}, {"name": "Load \"controller\" to Application", "start": 1748253139934, "end": 1748253140003, "duration": 69, "pid": 28812, "index": 78}, {"name": "Load Router", "start": 1748253140004, "end": 1748253140016, "duration": 12, "pid": 28812, "index": 79}, {"name": "Require(57) app/router.js", "start": 1748253140004, "end": 1748253140006, "duration": 2, "pid": 28812, "index": 80}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748253140009, "end": 1748253140080, "duration": 71, "pid": 28812, "index": 81}]