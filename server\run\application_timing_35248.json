[{"name": "Process Start", "start": 1748248554362, "end": 1748248556475, "duration": 2113, "pid": 35248, "index": 0}, {"name": "Application Start", "start": 1748248556477, "end": 1748248558730, "duration": 2253, "pid": 35248, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748248556498, "end": 1748248556557, "duration": 59, "pid": 35248, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748248556557, "end": 1748248556620, "duration": 63, "pid": 35248, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748248556558, "end": 1748248556559, "duration": 1, "pid": 35248, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748248556563, "end": 1748248556564, "duration": 1, "pid": 35248, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748248556566, "end": 1748248556567, "duration": 1, "pid": 35248, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748248556568, "end": 1748248556569, "duration": 1, "pid": 35248, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748248556570, "end": 1748248556571, "duration": 1, "pid": 35248, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748248556572, "end": 1748248556573, "duration": 1, "pid": 35248, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748248556574, "end": 1748248556575, "duration": 1, "pid": 35248, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748248556576, "end": 1748248556576, "duration": 0, "pid": 35248, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748248556577, "end": 1748248556579, "duration": 2, "pid": 35248, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748248556581, "end": 1748248556582, "duration": 1, "pid": 35248, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748248556584, "end": 1748248556585, "duration": 1, "pid": 35248, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748248556586, "end": 1748248556587, "duration": 1, "pid": 35248, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748248556588, "end": 1748248556589, "duration": 1, "pid": 35248, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748248556590, "end": 1748248556591, "duration": 1, "pid": 35248, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748248556593, "end": 1748248556595, "duration": 2, "pid": 35248, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748248556596, "end": 1748248556597, "duration": 1, "pid": 35248, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748248556598, "end": 1748248556599, "duration": 1, "pid": 35248, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748248556599, "end": 1748248556600, "duration": 1, "pid": 35248, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748248556601, "end": 1748248556601, "duration": 0, "pid": 35248, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748248556602, "end": 1748248556603, "duration": 1, "pid": 35248, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748248556603, "end": 1748248556604, "duration": 1, "pid": 35248, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748248556606, "end": 1748248556606, "duration": 0, "pid": 35248, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748248556609, "end": 1748248556609, "duration": 0, "pid": 35248, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748248556611, "end": 1748248556612, "duration": 1, "pid": 35248, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748248556615, "end": 1748248556615, "duration": 0, "pid": 35248, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748248556619, "end": 1748248556620, "duration": 1, "pid": 35248, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748248556620, "end": 1748248556620, "duration": 0, "pid": 35248, "index": 30}, {"name": "Load extend/application.js", "start": 1748248556621, "end": 1748248556766, "duration": 145, "pid": 35248, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748248556623, "end": 1748248556624, "duration": 1, "pid": 35248, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748248556625, "end": 1748248556627, "duration": 2, "pid": 35248, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748248556629, "end": 1748248556636, "duration": 7, "pid": 35248, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748248556638, "end": 1748248556649, "duration": 11, "pid": 35248, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748248556650, "end": 1748248556653, "duration": 3, "pid": 35248, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748248556655, "end": 1748248556659, "duration": 4, "pid": 35248, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748248556661, "end": 1748248556753, "duration": 92, "pid": 35248, "index": 38}, {"name": "Load extend/request.js", "start": 1748248556766, "end": 1748248556798, "duration": 32, "pid": 35248, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748248556776, "end": 1748248556785, "duration": 9, "pid": 35248, "index": 40}, {"name": "Load extend/response.js", "start": 1748248556798, "end": 1748248556826, "duration": 28, "pid": 35248, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748248556810, "end": 1748248556814, "duration": 4, "pid": 35248, "index": 42}, {"name": "Load extend/context.js", "start": 1748248556826, "end": 1748248556920, "duration": 94, "pid": 35248, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748248556827, "end": 1748248556851, "duration": 24, "pid": 35248, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748248556851, "end": 1748248556854, "duration": 3, "pid": 35248, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748248556855, "end": 1748248556858, "duration": 3, "pid": 35248, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748248556860, "end": 1748248556899, "duration": 39, "pid": 35248, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748248556901, "end": 1748248556903, "duration": 2, "pid": 35248, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748248556905, "end": 1748248556906, "duration": 1, "pid": 35248, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748248556907, "end": 1748248556910, "duration": 3, "pid": 35248, "index": 50}, {"name": "Load extend/helper.js", "start": 1748248556920, "end": 1748248556971, "duration": 51, "pid": 35248, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748248556921, "end": 1748248556954, "duration": 33, "pid": 35248, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748248556960, "end": 1748248556960, "duration": 0, "pid": 35248, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748248556961, "end": 1748248556962, "duration": 1, "pid": 35248, "index": 54}, {"name": "Load app.js", "start": 1748248556973, "end": 1748248557079, "duration": 106, "pid": 35248, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748248556973, "end": 1748248556974, "duration": 1, "pid": 35248, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748248556974, "end": 1748248556977, "duration": 3, "pid": 35248, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748248556978, "end": 1748248556995, "duration": 17, "pid": 35248, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748248556996, "end": 1748248557013, "duration": 17, "pid": 35248, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748248557014, "end": 1748248557032, "duration": 18, "pid": 35248, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748248557032, "end": 1748248557034, "duration": 2, "pid": 35248, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748248557034, "end": 1748248557036, "duration": 2, "pid": 35248, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748248557037, "end": 1748248557037, "duration": 0, "pid": 35248, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748248557038, "end": 1748248557038, "duration": 0, "pid": 35248, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748248557039, "end": 1748248557039, "duration": 0, "pid": 35248, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748248557040, "end": 1748248557041, "duration": 1, "pid": 35248, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748248557041, "end": 1748248557042, "duration": 1, "pid": 35248, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748248557042, "end": 1748248557043, "duration": 1, "pid": 35248, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748248557043, "end": 1748248557045, "duration": 2, "pid": 35248, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748248557046, "end": 1748248557070, "duration": 24, "pid": 35248, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748248557070, "end": 1748248557077, "duration": 7, "pid": 35248, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748248557094, "end": 1748248558699, "duration": 1605, "pid": 35248, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748248557841, "end": 1748248557913, "duration": 72, "pid": 35248, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748248557865, "end": 1748248558622, "duration": 757, "pid": 35248, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748248557945, "end": 1748248558729, "duration": 784, "pid": 35248, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748248558050, "end": 1748248558702, "duration": 652, "pid": 35248, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748248558167, "end": 1748248558649, "duration": 482, "pid": 35248, "index": 77}, {"name": "Load Service", "start": 1748248558168, "end": 1748248558319, "duration": 151, "pid": 35248, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748248558168, "end": 1748248558319, "duration": 151, "pid": 35248, "index": 79}, {"name": "Load Middleware", "start": 1748248558319, "end": 1748248558495, "duration": 176, "pid": 35248, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748248558319, "end": 1748248558479, "duration": 160, "pid": 35248, "index": 81}, {"name": "Load Controller", "start": 1748248558495, "end": 1748248558545, "duration": 50, "pid": 35248, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748248558495, "end": 1748248558545, "duration": 50, "pid": 35248, "index": 83}, {"name": "Load Router", "start": 1748248558545, "end": 1748248558552, "duration": 7, "pid": 35248, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748248558545, "end": 1748248558546, "duration": 1, "pid": 35248, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748248558547, "end": 1748248558622, "duration": 75, "pid": 35248, "index": 86}]