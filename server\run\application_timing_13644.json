[{"name": "Process Start", "start": 1748254738214, "end": 1748254745527, "duration": 7313, "pid": 13644, "index": 0}, {"name": "Application Start", "start": 1748254745529, "end": 1748254749535, "duration": 4006, "pid": 13644, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748254745577, "end": 1748254745658, "duration": 81, "pid": 13644, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748254745658, "end": 1748254745747, "duration": 89, "pid": 13644, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748254745659, "end": 1748254745661, "duration": 2, "pid": 13644, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748254745664, "end": 1748254745665, "duration": 1, "pid": 13644, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748254745667, "end": 1748254745668, "duration": 1, "pid": 13644, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748254745671, "end": 1748254745672, "duration": 1, "pid": 13644, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748254745675, "end": 1748254745676, "duration": 1, "pid": 13644, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748254745677, "end": 1748254745678, "duration": 1, "pid": 13644, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748254745679, "end": 1748254745680, "duration": 1, "pid": 13644, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748254745681, "end": 1748254745682, "duration": 1, "pid": 13644, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748254745684, "end": 1748254745684, "duration": 0, "pid": 13644, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748254745686, "end": 1748254745689, "duration": 3, "pid": 13644, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748254745691, "end": 1748254745692, "duration": 1, "pid": 13644, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748254745694, "end": 1748254745699, "duration": 5, "pid": 13644, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748254745701, "end": 1748254745703, "duration": 2, "pid": 13644, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748254745705, "end": 1748254745707, "duration": 2, "pid": 13644, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748254745709, "end": 1748254745711, "duration": 2, "pid": 13644, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748254745713, "end": 1748254745714, "duration": 1, "pid": 13644, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748254745716, "end": 1748254745717, "duration": 1, "pid": 13644, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748254745719, "end": 1748254745719, "duration": 0, "pid": 13644, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748254745721, "end": 1748254745723, "duration": 2, "pid": 13644, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748254745725, "end": 1748254745726, "duration": 1, "pid": 13644, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748254745727, "end": 1748254745728, "duration": 1, "pid": 13644, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748254745731, "end": 1748254745731, "duration": 0, "pid": 13644, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748254745733, "end": 1748254745734, "duration": 1, "pid": 13644, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748254745736, "end": 1748254745738, "duration": 2, "pid": 13644, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748254745741, "end": 1748254745742, "duration": 1, "pid": 13644, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748254745745, "end": 1748254745746, "duration": 1, "pid": 13644, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748254745746, "end": 1748254745746, "duration": 0, "pid": 13644, "index": 30}, {"name": "Load extend/application.js", "start": 1748254745750, "end": 1748254745950, "duration": 200, "pid": 13644, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748254745752, "end": 1748254745753, "duration": 1, "pid": 13644, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748254745754, "end": 1748254745759, "duration": 5, "pid": 13644, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748254745760, "end": 1748254745773, "duration": 13, "pid": 13644, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748254745776, "end": 1748254745791, "duration": 15, "pid": 13644, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748254745793, "end": 1748254745811, "duration": 18, "pid": 13644, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748254745813, "end": 1748254745818, "duration": 5, "pid": 13644, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748254745821, "end": 1748254745939, "duration": 118, "pid": 13644, "index": 38}, {"name": "Load extend/request.js", "start": 1748254745950, "end": 1748254745979, "duration": 29, "pid": 13644, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748254745960, "end": 1748254745962, "duration": 2, "pid": 13644, "index": 40}, {"name": "Load extend/response.js", "start": 1748254745979, "end": 1748254746006, "duration": 27, "pid": 13644, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748254745991, "end": 1748254745996, "duration": 5, "pid": 13644, "index": 42}, {"name": "Load extend/context.js", "start": 1748254746006, "end": 1748254746135, "duration": 129, "pid": 13644, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748254746007, "end": 1748254746051, "duration": 44, "pid": 13644, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748254746052, "end": 1748254746057, "duration": 5, "pid": 13644, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748254746058, "end": 1748254746060, "duration": 2, "pid": 13644, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748254746062, "end": 1748254746111, "duration": 49, "pid": 13644, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748254746113, "end": 1748254746115, "duration": 2, "pid": 13644, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748254746118, "end": 1748254746119, "duration": 1, "pid": 13644, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748254746120, "end": 1748254746125, "duration": 5, "pid": 13644, "index": 50}, {"name": "Load extend/helper.js", "start": 1748254746135, "end": 1748254746202, "duration": 67, "pid": 13644, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748254746138, "end": 1748254746184, "duration": 46, "pid": 13644, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748254746191, "end": 1748254746192, "duration": 1, "pid": 13644, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748254746193, "end": 1748254746194, "duration": 1, "pid": 13644, "index": 54}, {"name": "Load app.js", "start": 1748254746202, "end": 1748254746322, "duration": 120, "pid": 13644, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748254746203, "end": 1748254746203, "duration": 0, "pid": 13644, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748254746205, "end": 1748254746208, "duration": 3, "pid": 13644, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748254746209, "end": 1748254746228, "duration": 19, "pid": 13644, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748254746229, "end": 1748254746251, "duration": 22, "pid": 13644, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748254746252, "end": 1748254746269, "duration": 17, "pid": 13644, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748254746270, "end": 1748254746271, "duration": 1, "pid": 13644, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748254746272, "end": 1748254746275, "duration": 3, "pid": 13644, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748254746275, "end": 1748254746276, "duration": 1, "pid": 13644, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748254746276, "end": 1748254746277, "duration": 1, "pid": 13644, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748254746277, "end": 1748254746278, "duration": 1, "pid": 13644, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748254746279, "end": 1748254746280, "duration": 1, "pid": 13644, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748254746280, "end": 1748254746281, "duration": 1, "pid": 13644, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748254746281, "end": 1748254746282, "duration": 1, "pid": 13644, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748254746283, "end": 1748254746286, "duration": 3, "pid": 13644, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748254746286, "end": 1748254746313, "duration": 27, "pid": 13644, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748254746313, "end": 1748254746320, "duration": 7, "pid": 13644, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748254746335, "end": 1748254749508, "duration": 3173, "pid": 13644, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748254747774, "end": 1748254747909, "duration": 135, "pid": 13644, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748254747813, "end": 1748254749405, "duration": 1592, "pid": 13644, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748254747956, "end": 1748254749535, "duration": 1579, "pid": 13644, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748254748217, "end": 1748254749513, "duration": 1296, "pid": 13644, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748254748512, "end": 1748254749455, "duration": 943, "pid": 13644, "index": 77}, {"name": "Load Service", "start": 1748254748513, "end": 1748254748800, "duration": 287, "pid": 13644, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748254748513, "end": 1748254748800, "duration": 287, "pid": 13644, "index": 79}, {"name": "Load Middleware", "start": 1748254748800, "end": 1748254749234, "duration": 434, "pid": 13644, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748254748800, "end": 1748254749201, "duration": 401, "pid": 13644, "index": 81}, {"name": "Load Controller", "start": 1748254749234, "end": 1748254749317, "duration": 83, "pid": 13644, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748254749234, "end": 1748254749317, "duration": 83, "pid": 13644, "index": 83}, {"name": "Load Router", "start": 1748254749317, "end": 1748254749328, "duration": 11, "pid": 13644, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748254749318, "end": 1748254749320, "duration": 2, "pid": 13644, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748254749322, "end": 1748254749405, "duration": 83, "pid": 13644, "index": 86}]