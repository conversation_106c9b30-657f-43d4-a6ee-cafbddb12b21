[{"name": "Process Start", "start": 1748253133053, "end": 1748253135654, "duration": 2601, "pid": 13932, "index": 0}, {"name": "Application Start", "start": 1748253135657, "end": 1748253139987, "duration": 4330, "pid": 13932, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748253135695, "end": 1748253135809, "duration": 114, "pid": 13932, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748253135809, "end": 1748253135943, "duration": 134, "pid": 13932, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748253135810, "end": 1748253135811, "duration": 1, "pid": 13932, "index": 4}, {"name": "Require(1) config/config.prod.js", "start": 1748253135816, "end": 1748253135817, "duration": 1, "pid": 13932, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748253135821, "end": 1748253135826, "duration": 5, "pid": 13932, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748253135829, "end": 1748253135830, "duration": 1, "pid": 13932, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748253135839, "end": 1748253135840, "duration": 1, "pid": 13932, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748253135841, "end": 1748253135842, "duration": 1, "pid": 13932, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748253135844, "end": 1748253135844, "duration": 0, "pid": 13932, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748253135845, "end": 1748253135846, "duration": 1, "pid": 13932, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748253135847, "end": 1748253135848, "duration": 1, "pid": 13932, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748253135849, "end": 1748253135850, "duration": 1, "pid": 13932, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1748253135851, "end": 1748253135855, "duration": 4, "pid": 13932, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1748253135856, "end": 1748253135857, "duration": 1, "pid": 13932, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1748253135859, "end": 1748253135860, "duration": 1, "pid": 13932, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1748253135862, "end": 1748253135863, "duration": 1, "pid": 13932, "index": 17}, {"name": "Require(14) node_modules/egg-jwt/config/config.default.js", "start": 1748253135864, "end": 1748253135864, "duration": 0, "pid": 13932, "index": 18}, {"name": "Require(15) node_modules/egg-cors/config/config.default.js", "start": 1748253135865, "end": 1748253135866, "duration": 1, "pid": 13932, "index": 19}, {"name": "Require(16) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748253135867, "end": 1748253135868, "duration": 1, "pid": 13932, "index": 20}, {"name": "Require(17) node_modules/egg-mysql/config/config.default.js", "start": 1748253135870, "end": 1748253135870, "duration": 0, "pid": 13932, "index": 21}, {"name": "Require(18) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748253135871, "end": 1748253135877, "duration": 6, "pid": 13932, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748253135879, "end": 1748253135881, "duration": 2, "pid": 13932, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748253135894, "end": 1748253135894, "duration": 0, "pid": 13932, "index": 24}, {"name": "Require(21) node_modules/egg-static/config/config.prod.js", "start": 1748253135930, "end": 1748253135931, "duration": 1, "pid": 13932, "index": 25}, {"name": "Require(22) config/config.prod.js", "start": 1748253135943, "end": 1748253135943, "duration": 0, "pid": 13932, "index": 26}, {"name": "Load extend/application.js", "start": 1748253135946, "end": 1748253136217, "duration": 271, "pid": 13932, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1748253135947, "end": 1748253135948, "duration": 1, "pid": 13932, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1748253135950, "end": 1748253135953, "duration": 3, "pid": 13932, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1748253135956, "end": 1748253135975, "duration": 19, "pid": 13932, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1748253135978, "end": 1748253135988, "duration": 10, "pid": 13932, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1748253135990, "end": 1748253135993, "duration": 3, "pid": 13932, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1748253135994, "end": 1748253135997, "duration": 3, "pid": 13932, "index": 33}, {"name": "Require(29) node_modules/egg-jwt/app/extend/application.js", "start": 1748253136000, "end": 1748253136183, "duration": 183, "pid": 13932, "index": 34}, {"name": "Load extend/request.js", "start": 1748253136217, "end": 1748253136264, "duration": 47, "pid": 13932, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1748253136231, "end": 1748253136234, "duration": 3, "pid": 13932, "index": 36}, {"name": "Load extend/response.js", "start": 1748253136264, "end": 1748253136353, "duration": 89, "pid": 13932, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1748253136335, "end": 1748253136340, "duration": 5, "pid": 13932, "index": 38}, {"name": "Load extend/context.js", "start": 1748253136353, "end": 1748253136516, "duration": 163, "pid": 13932, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1748253136356, "end": 1748253136388, "duration": 32, "pid": 13932, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1748253136389, "end": 1748253136392, "duration": 3, "pid": 13932, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1748253136394, "end": 1748253136394, "duration": 0, "pid": 13932, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1748253136397, "end": 1748253136483, "duration": 86, "pid": 13932, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1748253136485, "end": 1748253136488, "duration": 3, "pid": 13932, "index": 44}, {"name": "Require(37) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748253136492, "end": 1748253136493, "duration": 1, "pid": 13932, "index": 45}, {"name": "Require(38) node_modules/egg/app/extend/context.js", "start": 1748253136495, "end": 1748253136503, "duration": 8, "pid": 13932, "index": 46}, {"name": "Load extend/helper.js", "start": 1748253136516, "end": 1748253136640, "duration": 124, "pid": 13932, "index": 47}, {"name": "Require(39) node_modules/egg-security/app/extend/helper.js", "start": 1748253136518, "end": 1748253136583, "duration": 65, "pid": 13932, "index": 48}, {"name": "Require(40) node_modules/egg/app/extend/helper.js", "start": 1748253136623, "end": 1748253136623, "duration": 0, "pid": 13932, "index": 49}, {"name": "Require(41) app/extend/helper.js", "start": 1748253136624, "end": 1748253136625, "duration": 1, "pid": 13932, "index": 50}, {"name": "Load app.js", "start": 1748253136640, "end": 1748253136807, "duration": 167, "pid": 13932, "index": 51}, {"name": "Require(42) node_modules/egg-session/app.js", "start": 1748253136641, "end": 1748253136642, "duration": 1, "pid": 13932, "index": 52}, {"name": "Require(43) node_modules/egg-security/app.js", "start": 1748253136643, "end": 1748253136648, "duration": 5, "pid": 13932, "index": 53}, {"name": "Require(44) node_modules/egg-onerror/app.js", "start": 1748253136653, "end": 1748253136674, "duration": 21, "pid": 13932, "index": 54}, {"name": "Require(45) node_modules/egg-i18n/app.js", "start": 1748253136675, "end": 1748253136702, "duration": 27, "pid": 13932, "index": 55}, {"name": "Require(46) node_modules/egg-watcher/app.js", "start": 1748253136703, "end": 1748253136738, "duration": 35, "pid": 13932, "index": 56}, {"name": "Require(47) node_modules/egg-schedule/app.js", "start": 1748253136738, "end": 1748253136740, "duration": 2, "pid": 13932, "index": 57}, {"name": "Require(48) node_modules/egg-multipart/app.js", "start": 1748253136741, "end": 1748253136744, "duration": 3, "pid": 13932, "index": 58}, {"name": "Require(49) node_modules/egg-logrotator/app.js", "start": 1748253136745, "end": 1748253136746, "duration": 1, "pid": 13932, "index": 59}, {"name": "Require(50) node_modules/egg-static/app.js", "start": 1748253136747, "end": 1748253136747, "duration": 0, "pid": 13932, "index": 60}, {"name": "Require(51) node_modules/egg-sequelize/app.js", "start": 1748253136749, "end": 1748253136749, "duration": 0, "pid": 13932, "index": 61}, {"name": "Require(52) node_modules/egg-jwt/app.js", "start": 1748253136750, "end": 1748253136751, "duration": 1, "pid": 13932, "index": 62}, {"name": "Require(53) node_modules/egg-cors/app.js", "start": 1748253136752, "end": 1748253136753, "duration": 1, "pid": 13932, "index": 63}, {"name": "Require(54) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748253136757, "end": 1748253136761, "duration": 4, "pid": 13932, "index": 64}, {"name": "Require(55) node_modules/egg-mysql/app.js", "start": 1748253136761, "end": 1748253136797, "duration": 36, "pid": 13932, "index": 65}, {"name": "Require(56) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748253136797, "end": 1748253136805, "duration": 8, "pid": 13932, "index": 66}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748253136857, "end": 1748253139955, "duration": 3098, "pid": 13932, "index": 67}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748253138375, "end": 1748253138530, "duration": 155, "pid": 13932, "index": 68}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748253138414, "end": 1748253139878, "duration": 1464, "pid": 13932, "index": 69}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748253138615, "end": 1748253139987, "duration": 1372, "pid": 13932, "index": 70}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748253138833, "end": 1748253139959, "duration": 1126, "pid": 13932, "index": 71}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748253139008, "end": 1748253139918, "duration": 910, "pid": 13932, "index": 72}, {"name": "Load Service", "start": 1748253139008, "end": 1748253139352, "duration": 344, "pid": 13932, "index": 73}, {"name": "Load \"service\" to Context", "start": 1748253139008, "end": 1748253139352, "duration": 344, "pid": 13932, "index": 74}, {"name": "Load Middleware", "start": 1748253139352, "end": 1748253139688, "duration": 336, "pid": 13932, "index": 75}, {"name": "Load \"middlewares\" to Application", "start": 1748253139353, "end": 1748253139656, "duration": 303, "pid": 13932, "index": 76}, {"name": "Load Controller", "start": 1748253139688, "end": 1748253139796, "duration": 108, "pid": 13932, "index": 77}, {"name": "Load \"controller\" to Application", "start": 1748253139688, "end": 1748253139796, "duration": 108, "pid": 13932, "index": 78}, {"name": "Load Router", "start": 1748253139796, "end": 1748253139809, "duration": 13, "pid": 13932, "index": 79}, {"name": "Require(57) app/router.js", "start": 1748253139797, "end": 1748253139798, "duration": 1, "pid": 13932, "index": 80}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748253139800, "end": 1748253139878, "duration": 78, "pid": 13932, "index": 81}]