[{"name": "Process Start", "start": 1748252875728, "end": 1748252882031, "duration": 6303, "pid": 32552, "index": 0}, {"name": "Application Start", "start": 1748252882040, "end": 1748252894252, "duration": 12212, "pid": 32552, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748252882120, "end": 1748252882263, "duration": 143, "pid": 32552, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748252882264, "end": 1748252882431, "duration": 167, "pid": 32552, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748252882268, "end": 1748252882270, "duration": 2, "pid": 32552, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748252882275, "end": 1748252882276, "duration": 1, "pid": 32552, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748252882282, "end": 1748252882284, "duration": 2, "pid": 32552, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748252882287, "end": 1748252882290, "duration": 3, "pid": 32552, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748252882293, "end": 1748252882295, "duration": 2, "pid": 32552, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748252882297, "end": 1748252882299, "duration": 2, "pid": 32552, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748252882302, "end": 1748252882305, "duration": 3, "pid": 32552, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748252882307, "end": 1748252882308, "duration": 1, "pid": 32552, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748252882310, "end": 1748252882311, "duration": 1, "pid": 32552, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748252882313, "end": 1748252882317, "duration": 4, "pid": 32552, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748252882320, "end": 1748252882322, "duration": 2, "pid": 32552, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748252882326, "end": 1748252882329, "duration": 3, "pid": 32552, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748252882331, "end": 1748252882341, "duration": 10, "pid": 32552, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748252882345, "end": 1748252882347, "duration": 2, "pid": 32552, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748252882356, "end": 1748252882360, "duration": 4, "pid": 32552, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748252882367, "end": 1748252882370, "duration": 3, "pid": 32552, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748252882372, "end": 1748252882377, "duration": 5, "pid": 32552, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748252882379, "end": 1748252882381, "duration": 2, "pid": 32552, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748252882386, "end": 1748252882388, "duration": 2, "pid": 32552, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748252882390, "end": 1748252882392, "duration": 2, "pid": 32552, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748252882393, "end": 1748252882395, "duration": 2, "pid": 32552, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748252882400, "end": 1748252882400, "duration": 0, "pid": 32552, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748252882405, "end": 1748252882406, "duration": 1, "pid": 32552, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748252882411, "end": 1748252882412, "duration": 1, "pid": 32552, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748252882421, "end": 1748252882423, "duration": 2, "pid": 32552, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748252882429, "end": 1748252882430, "duration": 1, "pid": 32552, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748252882430, "end": 1748252882430, "duration": 0, "pid": 32552, "index": 30}, {"name": "Load extend/application.js", "start": 1748252882439, "end": 1748252882721, "duration": 282, "pid": 32552, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748252882441, "end": 1748252882442, "duration": 1, "pid": 32552, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748252882444, "end": 1748252882453, "duration": 9, "pid": 32552, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748252882455, "end": 1748252882471, "duration": 16, "pid": 32552, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748252882474, "end": 1748252882495, "duration": 21, "pid": 32552, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748252882499, "end": 1748252882505, "duration": 6, "pid": 32552, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748252882507, "end": 1748252882512, "duration": 5, "pid": 32552, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748252882516, "end": 1748252882677, "duration": 161, "pid": 32552, "index": 38}, {"name": "Load extend/request.js", "start": 1748252882721, "end": 1748252882796, "duration": 75, "pid": 32552, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748252882771, "end": 1748252882777, "duration": 6, "pid": 32552, "index": 40}, {"name": "Load extend/response.js", "start": 1748252882796, "end": 1748252882976, "duration": 180, "pid": 32552, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748252882876, "end": 1748252882928, "duration": 52, "pid": 32552, "index": 42}, {"name": "Load extend/context.js", "start": 1748252882976, "end": 1748252883417, "duration": 441, "pid": 32552, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748252882979, "end": 1748252883073, "duration": 94, "pid": 32552, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748252883075, "end": 1748252883089, "duration": 14, "pid": 32552, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748252883093, "end": 1748252883095, "duration": 2, "pid": 32552, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748252883105, "end": 1748252883204, "duration": 99, "pid": 32552, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748252883207, "end": 1748252883210, "duration": 3, "pid": 32552, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748252883247, "end": 1748252883253, "duration": 6, "pid": 32552, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748252883261, "end": 1748252883296, "duration": 35, "pid": 32552, "index": 50}, {"name": "Load extend/helper.js", "start": 1748252883419, "end": 1748252885545, "duration": 2126, "pid": 32552, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748252883431, "end": 1748252884485, "duration": 1054, "pid": 32552, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748252885373, "end": 1748252885380, "duration": 7, "pid": 32552, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748252885394, "end": 1748252885396, "duration": 2, "pid": 32552, "index": 54}, {"name": "Load app.js", "start": 1748252885551, "end": 1748252887140, "duration": 1589, "pid": 32552, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748252885679, "end": 1748252885682, "duration": 3, "pid": 32552, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748252885691, "end": 1748252885777, "duration": 86, "pid": 32552, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748252885795, "end": 1748252886257, "duration": 462, "pid": 32552, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748252886268, "end": 1748252886570, "duration": 302, "pid": 32552, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748252886571, "end": 1748252886824, "duration": 253, "pid": 32552, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748252886825, "end": 1748252886830, "duration": 5, "pid": 32552, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748252886833, "end": 1748252886841, "duration": 8, "pid": 32552, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748252886842, "end": 1748252886845, "duration": 3, "pid": 32552, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748252886846, "end": 1748252886848, "duration": 2, "pid": 32552, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748252886849, "end": 1748252886849, "duration": 0, "pid": 32552, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748252886854, "end": 1748252886857, "duration": 3, "pid": 32552, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748252886858, "end": 1748252886859, "duration": 1, "pid": 32552, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748252886860, "end": 1748252886862, "duration": 2, "pid": 32552, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748252886863, "end": 1748252886885, "duration": 22, "pid": 32552, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748252886887, "end": 1748252887068, "duration": 181, "pid": 32552, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748252887069, "end": 1748252887135, "duration": 66, "pid": 32552, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748252887410, "end": 1748252894201, "duration": 6791, "pid": 32552, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748252890508, "end": 1748252891179, "duration": 671, "pid": 32552, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748252890572, "end": 1748252894050, "duration": 3478, "pid": 32552, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748252891334, "end": 1748252894250, "duration": 2916, "pid": 32552, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748252892040, "end": 1748252894195, "duration": 2155, "pid": 32552, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748252892455, "end": 1748252894091, "duration": 1636, "pid": 32552, "index": 77}, {"name": "Load Service", "start": 1748252892455, "end": 1748252893005, "duration": 550, "pid": 32552, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748252892456, "end": 1748252893005, "duration": 549, "pid": 32552, "index": 79}, {"name": "Load Middleware", "start": 1748252893006, "end": 1748252893665, "duration": 659, "pid": 32552, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748252893006, "end": 1748252893619, "duration": 613, "pid": 32552, "index": 81}, {"name": "Load Controller", "start": 1748252893665, "end": 1748252893912, "duration": 247, "pid": 32552, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748252893665, "end": 1748252893912, "duration": 247, "pid": 32552, "index": 83}, {"name": "Load Router", "start": 1748252893912, "end": 1748252893932, "duration": 20, "pid": 32552, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748252893915, "end": 1748252893917, "duration": 2, "pid": 32552, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748252893919, "end": 1748252894050, "duration": 131, "pid": 32552, "index": 86}]