[{"name": "Process Start", "start": 1748248905637, "end": 1748248908509, "duration": 2872, "pid": 12556, "index": 0}, {"name": "Application Start", "start": 1748248908513, "end": 1748248911590, "duration": 3077, "pid": 12556, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748248908562, "end": 1748248908627, "duration": 65, "pid": 12556, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748248908627, "end": 1748248908691, "duration": 64, "pid": 12556, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748248908629, "end": 1748248908630, "duration": 1, "pid": 12556, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748248908635, "end": 1748248908636, "duration": 1, "pid": 12556, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748248908637, "end": 1748248908638, "duration": 1, "pid": 12556, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748248908639, "end": 1748248908640, "duration": 1, "pid": 12556, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748248908641, "end": 1748248908642, "duration": 1, "pid": 12556, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748248908643, "end": 1748248908644, "duration": 1, "pid": 12556, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748248908645, "end": 1748248908646, "duration": 1, "pid": 12556, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748248908647, "end": 1748248908648, "duration": 1, "pid": 12556, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748248908650, "end": 1748248908650, "duration": 0, "pid": 12556, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748248908651, "end": 1748248908652, "duration": 1, "pid": 12556, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748248908654, "end": 1748248908655, "duration": 1, "pid": 12556, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748248908656, "end": 1748248908657, "duration": 1, "pid": 12556, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748248908659, "end": 1748248908660, "duration": 1, "pid": 12556, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748248908662, "end": 1748248908663, "duration": 1, "pid": 12556, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748248908665, "end": 1748248908665, "duration": 0, "pid": 12556, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748248908666, "end": 1748248908667, "duration": 1, "pid": 12556, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748248908668, "end": 1748248908669, "duration": 1, "pid": 12556, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748248908669, "end": 1748248908670, "duration": 1, "pid": 12556, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748248908671, "end": 1748248908671, "duration": 0, "pid": 12556, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748248908673, "end": 1748248908674, "duration": 1, "pid": 12556, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748248908675, "end": 1748248908676, "duration": 1, "pid": 12556, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748248908678, "end": 1748248908678, "duration": 0, "pid": 12556, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748248908680, "end": 1748248908680, "duration": 0, "pid": 12556, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748248908682, "end": 1748248908683, "duration": 1, "pid": 12556, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748248908686, "end": 1748248908687, "duration": 1, "pid": 12556, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748248908691, "end": 1748248908691, "duration": 0, "pid": 12556, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748248908691, "end": 1748248908691, "duration": 0, "pid": 12556, "index": 30}, {"name": "Load extend/application.js", "start": 1748248908693, "end": 1748248908841, "duration": 148, "pid": 12556, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748248908694, "end": 1748248908695, "duration": 1, "pid": 12556, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748248908696, "end": 1748248908700, "duration": 4, "pid": 12556, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748248908701, "end": 1748248908710, "duration": 9, "pid": 12556, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748248908714, "end": 1748248908726, "duration": 12, "pid": 12556, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748248908729, "end": 1748248908732, "duration": 3, "pid": 12556, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748248908733, "end": 1748248908736, "duration": 3, "pid": 12556, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748248908737, "end": 1748248908827, "duration": 90, "pid": 12556, "index": 38}, {"name": "Load extend/request.js", "start": 1748248908841, "end": 1748248908871, "duration": 30, "pid": 12556, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748248908852, "end": 1748248908856, "duration": 4, "pid": 12556, "index": 40}, {"name": "Load extend/response.js", "start": 1748248908871, "end": 1748248908898, "duration": 27, "pid": 12556, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748248908881, "end": 1748248908890, "duration": 9, "pid": 12556, "index": 42}, {"name": "Load extend/context.js", "start": 1748248908898, "end": 1748248909054, "duration": 156, "pid": 12556, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748248908902, "end": 1748248908948, "duration": 46, "pid": 12556, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748248908950, "end": 1748248908956, "duration": 6, "pid": 12556, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748248908958, "end": 1748248908959, "duration": 1, "pid": 12556, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748248908961, "end": 1748248909024, "duration": 63, "pid": 12556, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748248909027, "end": 1748248909030, "duration": 3, "pid": 12556, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748248909032, "end": 1748248909034, "duration": 2, "pid": 12556, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748248909036, "end": 1748248909041, "duration": 5, "pid": 12556, "index": 50}, {"name": "Load extend/helper.js", "start": 1748248909054, "end": 1748248909132, "duration": 78, "pid": 12556, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748248909056, "end": 1748248909105, "duration": 49, "pid": 12556, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748248909114, "end": 1748248909115, "duration": 1, "pid": 12556, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748248909116, "end": 1748248909117, "duration": 1, "pid": 12556, "index": 54}, {"name": "Load app.js", "start": 1748248909133, "end": 1748248909327, "duration": 194, "pid": 12556, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748248909133, "end": 1748248909134, "duration": 1, "pid": 12556, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748248909135, "end": 1748248909141, "duration": 6, "pid": 12556, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748248909142, "end": 1748248909170, "duration": 28, "pid": 12556, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748248909171, "end": 1748248909206, "duration": 35, "pid": 12556, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748248909206, "end": 1748248909232, "duration": 26, "pid": 12556, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748248909233, "end": 1748248909236, "duration": 3, "pid": 12556, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748248909238, "end": 1748248909242, "duration": 4, "pid": 12556, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748248909243, "end": 1748248909243, "duration": 0, "pid": 12556, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748248909244, "end": 1748248909244, "duration": 0, "pid": 12556, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748248909247, "end": 1748248909249, "duration": 2, "pid": 12556, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748248909251, "end": 1748248909251, "duration": 0, "pid": 12556, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748248909252, "end": 1748248909253, "duration": 1, "pid": 12556, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748248909254, "end": 1748248909256, "duration": 2, "pid": 12556, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748248909258, "end": 1748248909266, "duration": 8, "pid": 12556, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748248909267, "end": 1748248909308, "duration": 41, "pid": 12556, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748248909309, "end": 1748248909323, "duration": 14, "pid": 12556, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748248909389, "end": 1748248911547, "duration": 2158, "pid": 12556, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748248910422, "end": 1748248910518, "duration": 96, "pid": 12556, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748248910451, "end": 1748248911430, "duration": 979, "pid": 12556, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748248910549, "end": 1748248911589, "duration": 1040, "pid": 12556, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748248910661, "end": 1748248911560, "duration": 899, "pid": 12556, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748248910786, "end": 1748248911456, "duration": 670, "pid": 12556, "index": 77}, {"name": "Load Service", "start": 1748248910786, "end": 1748248910966, "duration": 180, "pid": 12556, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748248910786, "end": 1748248910966, "duration": 180, "pid": 12556, "index": 79}, {"name": "Load Middleware", "start": 1748248910966, "end": 1748248911250, "duration": 284, "pid": 12556, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748248910966, "end": 1748248911229, "duration": 263, "pid": 12556, "index": 81}, {"name": "Load Controller", "start": 1748248911250, "end": 1748248911322, "duration": 72, "pid": 12556, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748248911250, "end": 1748248911322, "duration": 72, "pid": 12556, "index": 83}, {"name": "Load Router", "start": 1748248911322, "end": 1748248911334, "duration": 12, "pid": 12556, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748248911323, "end": 1748248911325, "duration": 2, "pid": 12556, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748248911327, "end": 1748248911430, "duration": 103, "pid": 12556, "index": 86}]