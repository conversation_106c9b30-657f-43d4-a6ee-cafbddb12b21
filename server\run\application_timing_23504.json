[{"name": "Process Start", "start": 1748252998387, "end": 1748253004364, "duration": 5977, "pid": 23504, "index": 0}, {"name": "Application Start", "start": 1748253004368, "end": 1748253011045, "duration": 6677, "pid": 23504, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748253004422, "end": 1748253004514, "duration": 92, "pid": 23504, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748253004514, "end": 1748253004615, "duration": 101, "pid": 23504, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748253004516, "end": 1748253004518, "duration": 2, "pid": 23504, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748253004521, "end": 1748253004522, "duration": 1, "pid": 23504, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748253004526, "end": 1748253004530, "duration": 4, "pid": 23504, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748253004531, "end": 1748253004532, "duration": 1, "pid": 23504, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748253004534, "end": 1748253004535, "duration": 1, "pid": 23504, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748253004536, "end": 1748253004536, "duration": 0, "pid": 23504, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748253004537, "end": 1748253004539, "duration": 2, "pid": 23504, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748253004541, "end": 1748253004541, "duration": 0, "pid": 23504, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748253004545, "end": 1748253004546, "duration": 1, "pid": 23504, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748253004548, "end": 1748253004549, "duration": 1, "pid": 23504, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748253004550, "end": 1748253004551, "duration": 1, "pid": 23504, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748253004552, "end": 1748253004553, "duration": 1, "pid": 23504, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748253004555, "end": 1748253004556, "duration": 1, "pid": 23504, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748253004558, "end": 1748253004561, "duration": 3, "pid": 23504, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748253004563, "end": 1748253004564, "duration": 1, "pid": 23504, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748253004565, "end": 1748253004566, "duration": 1, "pid": 23504, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748253004567, "end": 1748253004568, "duration": 1, "pid": 23504, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748253004571, "end": 1748253004571, "duration": 0, "pid": 23504, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748253004573, "end": 1748253004573, "duration": 0, "pid": 23504, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748253004577, "end": 1748253004579, "duration": 2, "pid": 23504, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748253004581, "end": 1748253004582, "duration": 1, "pid": 23504, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748253004588, "end": 1748253004588, "duration": 0, "pid": 23504, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748253004591, "end": 1748253004596, "duration": 5, "pid": 23504, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748253004599, "end": 1748253004601, "duration": 2, "pid": 23504, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748253004606, "end": 1748253004607, "duration": 1, "pid": 23504, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748253004614, "end": 1748253004615, "duration": 1, "pid": 23504, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748253004615, "end": 1748253004615, "duration": 0, "pid": 23504, "index": 30}, {"name": "Load extend/application.js", "start": 1748253004621, "end": 1748253004951, "duration": 330, "pid": 23504, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748253004623, "end": 1748253004624, "duration": 1, "pid": 23504, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748253004625, "end": 1748253004632, "duration": 7, "pid": 23504, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748253004634, "end": 1748253004666, "duration": 32, "pid": 23504, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748253004670, "end": 1748253004688, "duration": 18, "pid": 23504, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748253004690, "end": 1748253004696, "duration": 6, "pid": 23504, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748253004698, "end": 1748253004702, "duration": 4, "pid": 23504, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748253004704, "end": 1748253004932, "duration": 228, "pid": 23504, "index": 38}, {"name": "Load extend/request.js", "start": 1748253004951, "end": 1748253004989, "duration": 38, "pid": 23504, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748253004968, "end": 1748253004972, "duration": 4, "pid": 23504, "index": 40}, {"name": "Load extend/response.js", "start": 1748253004990, "end": 1748253005032, "duration": 42, "pid": 23504, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748253005006, "end": 1748253005015, "duration": 9, "pid": 23504, "index": 42}, {"name": "Load extend/context.js", "start": 1748253005032, "end": 1748253005286, "duration": 254, "pid": 23504, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748253005034, "end": 1748253005088, "duration": 54, "pid": 23504, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748253005089, "end": 1748253005096, "duration": 7, "pid": 23504, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748253005098, "end": 1748253005100, "duration": 2, "pid": 23504, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748253005103, "end": 1748253005231, "duration": 128, "pid": 23504, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748253005235, "end": 1748253005239, "duration": 4, "pid": 23504, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748253005248, "end": 1748253005249, "duration": 1, "pid": 23504, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748253005254, "end": 1748253005267, "duration": 13, "pid": 23504, "index": 50}, {"name": "Load extend/helper.js", "start": 1748253005287, "end": 1748253005421, "duration": 134, "pid": 23504, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748253005293, "end": 1748253005375, "duration": 82, "pid": 23504, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748253005392, "end": 1748253005396, "duration": 4, "pid": 23504, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748253005399, "end": 1748253005401, "duration": 2, "pid": 23504, "index": 54}, {"name": "Load app.js", "start": 1748253005422, "end": 1748253005708, "duration": 286, "pid": 23504, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748253005423, "end": 1748253005424, "duration": 1, "pid": 23504, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748253005425, "end": 1748253005437, "duration": 12, "pid": 23504, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748253005442, "end": 1748253005488, "duration": 46, "pid": 23504, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748253005489, "end": 1748253005537, "duration": 48, "pid": 23504, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748253005538, "end": 1748253005597, "duration": 59, "pid": 23504, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748253005598, "end": 1748253005601, "duration": 3, "pid": 23504, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748253005602, "end": 1748253005607, "duration": 5, "pid": 23504, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748253005608, "end": 1748253005611, "duration": 3, "pid": 23504, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748253005612, "end": 1748253005613, "duration": 1, "pid": 23504, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748253005614, "end": 1748253005615, "duration": 1, "pid": 23504, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748253005619, "end": 1748253005620, "duration": 1, "pid": 23504, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748253005621, "end": 1748253005621, "duration": 0, "pid": 23504, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748253005622, "end": 1748253005630, "duration": 8, "pid": 23504, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748253005631, "end": 1748253005639, "duration": 8, "pid": 23504, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748253005649, "end": 1748253005689, "duration": 40, "pid": 23504, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748253005690, "end": 1748253005703, "duration": 13, "pid": 23504, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748253005751, "end": 1748253011021, "duration": 5270, "pid": 23504, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748253009181, "end": 1748253009500, "duration": 319, "pid": 23504, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748253009352, "end": 1748253010923, "duration": 1571, "pid": 23504, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748253009618, "end": 1748253011045, "duration": 1427, "pid": 23504, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748253009896, "end": 1748253011017, "duration": 1121, "pid": 23504, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748253010050, "end": 1748253010963, "duration": 913, "pid": 23504, "index": 77}, {"name": "Load Service", "start": 1748253010050, "end": 1748253010411, "duration": 361, "pid": 23504, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748253010051, "end": 1748253010411, "duration": 360, "pid": 23504, "index": 79}, {"name": "Load Middleware", "start": 1748253010411, "end": 1748253010714, "duration": 303, "pid": 23504, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748253010412, "end": 1748253010691, "duration": 279, "pid": 23504, "index": 81}, {"name": "Load Controller", "start": 1748253010714, "end": 1748253010807, "duration": 93, "pid": 23504, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748253010715, "end": 1748253010807, "duration": 92, "pid": 23504, "index": 83}, {"name": "Load Router", "start": 1748253010807, "end": 1748253010821, "duration": 14, "pid": 23504, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748253010808, "end": 1748253010810, "duration": 2, "pid": 23504, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748253010812, "end": 1748253010923, "duration": 111, "pid": 23504, "index": 86}]