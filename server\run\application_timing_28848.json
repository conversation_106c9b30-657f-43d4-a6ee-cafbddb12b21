[{"name": "Process Start", "start": 1748248480950, "end": 1748248484426, "duration": 3476, "pid": 28848, "index": 0}, {"name": "Application Start", "start": 1748248484428, "end": 1748248487175, "duration": 2747, "pid": 28848, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748248484450, "end": 1748248484488, "duration": 38, "pid": 28848, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748248484488, "end": 1748248484537, "duration": 49, "pid": 28848, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748248484489, "end": 1748248484490, "duration": 1, "pid": 28848, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748248484492, "end": 1748248484492, "duration": 0, "pid": 28848, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748248484494, "end": 1748248484494, "duration": 0, "pid": 28848, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748248484498, "end": 1748248484499, "duration": 1, "pid": 28848, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748248484500, "end": 1748248484501, "duration": 1, "pid": 28848, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748248484502, "end": 1748248484502, "duration": 0, "pid": 28848, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748248484503, "end": 1748248484503, "duration": 0, "pid": 28848, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748248484504, "end": 1748248484505, "duration": 1, "pid": 28848, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748248484505, "end": 1748248484506, "duration": 1, "pid": 28848, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748248484507, "end": 1748248484507, "duration": 0, "pid": 28848, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748248484508, "end": 1748248484509, "duration": 1, "pid": 28848, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748248484510, "end": 1748248484510, "duration": 0, "pid": 28848, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748248484511, "end": 1748248484512, "duration": 1, "pid": 28848, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748248484512, "end": 1748248484513, "duration": 1, "pid": 28848, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748248484514, "end": 1748248484514, "duration": 0, "pid": 28848, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748248484515, "end": 1748248484515, "duration": 0, "pid": 28848, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748248484516, "end": 1748248484516, "duration": 0, "pid": 28848, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748248484517, "end": 1748248484517, "duration": 0, "pid": 28848, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748248484518, "end": 1748248484519, "duration": 1, "pid": 28848, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748248484519, "end": 1748248484520, "duration": 1, "pid": 28848, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748248484520, "end": 1748248484521, "duration": 1, "pid": 28848, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748248484522, "end": 1748248484522, "duration": 0, "pid": 28848, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748248484524, "end": 1748248484524, "duration": 0, "pid": 28848, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748248484527, "end": 1748248484527, "duration": 0, "pid": 28848, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748248484531, "end": 1748248484532, "duration": 1, "pid": 28848, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748248484536, "end": 1748248484537, "duration": 1, "pid": 28848, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748248484537, "end": 1748248484537, "duration": 0, "pid": 28848, "index": 30}, {"name": "Load extend/application.js", "start": 1748248484539, "end": 1748248484652, "duration": 113, "pid": 28848, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748248484542, "end": 1748248484543, "duration": 1, "pid": 28848, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748248484544, "end": 1748248484546, "duration": 2, "pid": 28848, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748248484547, "end": 1748248484554, "duration": 7, "pid": 28848, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748248484556, "end": 1748248484564, "duration": 8, "pid": 28848, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748248484565, "end": 1748248484568, "duration": 3, "pid": 28848, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748248484569, "end": 1748248484572, "duration": 3, "pid": 28848, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748248484573, "end": 1748248484643, "duration": 70, "pid": 28848, "index": 38}, {"name": "Load extend/request.js", "start": 1748248484653, "end": 1748248484672, "duration": 19, "pid": 28848, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748248484660, "end": 1748248484662, "duration": 2, "pid": 28848, "index": 40}, {"name": "Load extend/response.js", "start": 1748248484672, "end": 1748248484694, "duration": 22, "pid": 28848, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748248484681, "end": 1748248484686, "duration": 5, "pid": 28848, "index": 42}, {"name": "Load extend/context.js", "start": 1748248484694, "end": 1748248484776, "duration": 82, "pid": 28848, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748248484695, "end": 1748248484717, "duration": 22, "pid": 28848, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748248484718, "end": 1748248484721, "duration": 3, "pid": 28848, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748248484722, "end": 1748248484722, "duration": 0, "pid": 28848, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748248484724, "end": 1748248484757, "duration": 33, "pid": 28848, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748248484759, "end": 1748248484761, "duration": 2, "pid": 28848, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748248484763, "end": 1748248484764, "duration": 1, "pid": 28848, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748248484765, "end": 1748248484768, "duration": 3, "pid": 28848, "index": 50}, {"name": "Load extend/helper.js", "start": 1748248484776, "end": 1748248484822, "duration": 46, "pid": 28848, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748248484777, "end": 1748248484804, "duration": 27, "pid": 28848, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748248484811, "end": 1748248484812, "duration": 1, "pid": 28848, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748248484812, "end": 1748248484813, "duration": 1, "pid": 28848, "index": 54}, {"name": "Load app.js", "start": 1748248484823, "end": 1748248484928, "duration": 105, "pid": 28848, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748248484823, "end": 1748248484824, "duration": 1, "pid": 28848, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748248484824, "end": 1748248484827, "duration": 3, "pid": 28848, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748248484828, "end": 1748248484846, "duration": 18, "pid": 28848, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748248484847, "end": 1748248484864, "duration": 17, "pid": 28848, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748248484864, "end": 1748248484886, "duration": 22, "pid": 28848, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748248484886, "end": 1748248484887, "duration": 1, "pid": 28848, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748248484888, "end": 1748248484890, "duration": 2, "pid": 28848, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748248484891, "end": 1748248484891, "duration": 0, "pid": 28848, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748248484892, "end": 1748248484892, "duration": 0, "pid": 28848, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748248484893, "end": 1748248484893, "duration": 0, "pid": 28848, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748248484894, "end": 1748248484895, "duration": 1, "pid": 28848, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748248484895, "end": 1748248484896, "duration": 1, "pid": 28848, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748248484897, "end": 1748248484897, "duration": 0, "pid": 28848, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748248484898, "end": 1748248484901, "duration": 3, "pid": 28848, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748248484902, "end": 1748248484920, "duration": 18, "pid": 28848, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748248484920, "end": 1748248484926, "duration": 6, "pid": 28848, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748248484949, "end": 1748248487140, "duration": 2191, "pid": 28848, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748248486162, "end": 1748248486251, "duration": 89, "pid": 28848, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748248486187, "end": 1748248487041, "duration": 854, "pid": 28848, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748248486290, "end": 1748248487175, "duration": 885, "pid": 28848, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748248486388, "end": 1748248487151, "duration": 763, "pid": 28848, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748248486509, "end": 1748248487087, "duration": 578, "pid": 28848, "index": 77}, {"name": "Load Service", "start": 1748248486509, "end": 1748248486668, "duration": 159, "pid": 28848, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748248486509, "end": 1748248486668, "duration": 159, "pid": 28848, "index": 79}, {"name": "Load Middleware", "start": 1748248486668, "end": 1748248486887, "duration": 219, "pid": 28848, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748248486668, "end": 1748248486865, "duration": 197, "pid": 28848, "index": 81}, {"name": "Load Controller", "start": 1748248486887, "end": 1748248486947, "duration": 60, "pid": 28848, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748248486887, "end": 1748248486947, "duration": 60, "pid": 28848, "index": 83}, {"name": "Load Router", "start": 1748248486947, "end": 1748248486956, "duration": 9, "pid": 28848, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748248486949, "end": 1748248486950, "duration": 1, "pid": 28848, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748248486951, "end": 1748248487041, "duration": 90, "pid": 28848, "index": 86}]