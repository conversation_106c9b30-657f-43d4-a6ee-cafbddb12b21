[{"name": "Process Start", "start": 1748248477210, "end": 1748248479363, "duration": 2153, "pid": 11844, "index": 0}, {"name": "Application Start", "start": 1748248479364, "end": 1748248484041, "duration": 4677, "pid": 11844, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748248479385, "end": 1748248479422, "duration": 37, "pid": 11844, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748248479422, "end": 1748248479468, "duration": 46, "pid": 11844, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748248479423, "end": 1748248479424, "duration": 1, "pid": 11844, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748248479427, "end": 1748248479427, "duration": 0, "pid": 11844, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748248479428, "end": 1748248479428, "duration": 0, "pid": 11844, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748248479429, "end": 1748248479430, "duration": 1, "pid": 11844, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748248479434, "end": 1748248479434, "duration": 0, "pid": 11844, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748248479435, "end": 1748248479435, "duration": 0, "pid": 11844, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748248479436, "end": 1748248479436, "duration": 0, "pid": 11844, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748248479437, "end": 1748248479438, "duration": 1, "pid": 11844, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748248479438, "end": 1748248479439, "duration": 1, "pid": 11844, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748248479440, "end": 1748248479441, "duration": 1, "pid": 11844, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748248479441, "end": 1748248479442, "duration": 1, "pid": 11844, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748248479443, "end": 1748248479443, "duration": 0, "pid": 11844, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748248479444, "end": 1748248479444, "duration": 0, "pid": 11844, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748248479445, "end": 1748248479445, "duration": 0, "pid": 11844, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748248479447, "end": 1748248479447, "duration": 0, "pid": 11844, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748248479448, "end": 1748248479449, "duration": 1, "pid": 11844, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748248479450, "end": 1748248479450, "duration": 0, "pid": 11844, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748248479451, "end": 1748248479451, "duration": 0, "pid": 11844, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748248479452, "end": 1748248479452, "duration": 0, "pid": 11844, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748248479453, "end": 1748248479453, "duration": 0, "pid": 11844, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748248479454, "end": 1748248479454, "duration": 0, "pid": 11844, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748248479456, "end": 1748248479456, "duration": 0, "pid": 11844, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748248479457, "end": 1748248479458, "duration": 1, "pid": 11844, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748248479460, "end": 1748248479461, "duration": 1, "pid": 11844, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748248479464, "end": 1748248479464, "duration": 0, "pid": 11844, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748248479467, "end": 1748248479467, "duration": 0, "pid": 11844, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748248479467, "end": 1748248479467, "duration": 0, "pid": 11844, "index": 30}, {"name": "Load extend/application.js", "start": 1748248479469, "end": 1748248479891, "duration": 422, "pid": 11844, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748248479470, "end": 1748248479487, "duration": 17, "pid": 11844, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748248479488, "end": 1748248479491, "duration": 3, "pid": 11844, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748248479491, "end": 1748248479523, "duration": 32, "pid": 11844, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748248479526, "end": 1748248479543, "duration": 17, "pid": 11844, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748248479545, "end": 1748248479548, "duration": 3, "pid": 11844, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748248479549, "end": 1748248479565, "duration": 16, "pid": 11844, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748248479567, "end": 1748248479882, "duration": 315, "pid": 11844, "index": 38}, {"name": "Load extend/request.js", "start": 1748248479891, "end": 1748248479936, "duration": 45, "pid": 11844, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748248479927, "end": 1748248479929, "duration": 2, "pid": 11844, "index": 40}, {"name": "Load extend/response.js", "start": 1748248479936, "end": 1748248479982, "duration": 46, "pid": 11844, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748248479947, "end": 1748248479975, "duration": 28, "pid": 11844, "index": 42}, {"name": "Load extend/context.js", "start": 1748248479982, "end": 1748248480064, "duration": 82, "pid": 11844, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748248479983, "end": 1748248480006, "duration": 23, "pid": 11844, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748248480006, "end": 1748248480010, "duration": 4, "pid": 11844, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748248480012, "end": 1748248480013, "duration": 1, "pid": 11844, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748248480016, "end": 1748248480044, "duration": 28, "pid": 11844, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748248480046, "end": 1748248480048, "duration": 2, "pid": 11844, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748248480050, "end": 1748248480051, "duration": 1, "pid": 11844, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748248480052, "end": 1748248480055, "duration": 3, "pid": 11844, "index": 50}, {"name": "Load extend/helper.js", "start": 1748248480064, "end": 1748248480434, "duration": 370, "pid": 11844, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748248480066, "end": 1748248480320, "duration": 254, "pid": 11844, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748248480327, "end": 1748248480393, "duration": 66, "pid": 11844, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748248480395, "end": 1748248480425, "duration": 30, "pid": 11844, "index": 54}, {"name": "Load app.js", "start": 1748248480434, "end": 1748248480597, "duration": 163, "pid": 11844, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748248480435, "end": 1748248480435, "duration": 0, "pid": 11844, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748248480436, "end": 1748248480440, "duration": 4, "pid": 11844, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748248480442, "end": 1748248480484, "duration": 42, "pid": 11844, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748248480485, "end": 1748248480522, "duration": 37, "pid": 11844, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748248480523, "end": 1748248480542, "duration": 19, "pid": 11844, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748248480542, "end": 1748248480543, "duration": 1, "pid": 11844, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748248480544, "end": 1748248480547, "duration": 3, "pid": 11844, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748248480549, "end": 1748248480549, "duration": 0, "pid": 11844, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748248480550, "end": 1748248480551, "duration": 1, "pid": 11844, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748248480551, "end": 1748248480552, "duration": 1, "pid": 11844, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748248480553, "end": 1748248480553, "duration": 0, "pid": 11844, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748248480554, "end": 1748248480554, "duration": 0, "pid": 11844, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748248480555, "end": 1748248480556, "duration": 1, "pid": 11844, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748248480557, "end": 1748248480565, "duration": 8, "pid": 11844, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748248480566, "end": 1748248480587, "duration": 21, "pid": 11844, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748248480588, "end": 1748248480595, "duration": 7, "pid": 11844, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748248480615, "end": 1748248484040, "duration": 3425, "pid": 11844, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748248481790, "end": 1748248481960, "duration": 170, "pid": 11844, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748248481834, "end": 1748248483753, "duration": 1919, "pid": 11844, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748248482028, "end": 1748248483978, "duration": 1950, "pid": 11844, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748248482296, "end": 1748248483950, "duration": 1654, "pid": 11844, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748248482502, "end": 1748248483936, "duration": 1434, "pid": 11844, "index": 77}, {"name": "Load Service", "start": 1748248482503, "end": 1748248482866, "duration": 363, "pid": 11844, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748248482503, "end": 1748248482866, "duration": 363, "pid": 11844, "index": 79}, {"name": "Load Middleware", "start": 1748248482867, "end": 1748248483217, "duration": 350, "pid": 11844, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748248482867, "end": 1748248483183, "duration": 316, "pid": 11844, "index": 81}, {"name": "Load Controller", "start": 1748248483218, "end": 1748248483453, "duration": 235, "pid": 11844, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748248483218, "end": 1748248483453, "duration": 235, "pid": 11844, "index": 83}, {"name": "Load Router", "start": 1748248483453, "end": 1748248483469, "duration": 16, "pid": 11844, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748248483454, "end": 1748248483456, "duration": 2, "pid": 11844, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748248483458, "end": 1748248483753, "duration": 295, "pid": 11844, "index": 86}]