[{"name": "Process Start", "start": 1748254672594, "end": 1748254678379, "duration": 5785, "pid": 15216, "index": 0}, {"name": "Application Start", "start": 1748254678383, "end": 1748254681370, "duration": 2987, "pid": 15216, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748254678456, "end": 1748254678653, "duration": 197, "pid": 15216, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748254678653, "end": 1748254678741, "duration": 88, "pid": 15216, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748254678656, "end": 1748254678658, "duration": 2, "pid": 15216, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748254678664, "end": 1748254678665, "duration": 1, "pid": 15216, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748254678669, "end": 1748254678671, "duration": 2, "pid": 15216, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748254678672, "end": 1748254678673, "duration": 1, "pid": 15216, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748254678676, "end": 1748254678677, "duration": 1, "pid": 15216, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748254678678, "end": 1748254678679, "duration": 1, "pid": 15216, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748254678680, "end": 1748254678681, "duration": 1, "pid": 15216, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748254678682, "end": 1748254678684, "duration": 2, "pid": 15216, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748254678686, "end": 1748254678687, "duration": 1, "pid": 15216, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748254678690, "end": 1748254678691, "duration": 1, "pid": 15216, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748254678693, "end": 1748254678693, "duration": 0, "pid": 15216, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748254678695, "end": 1748254678695, "duration": 0, "pid": 15216, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748254678696, "end": 1748254678696, "duration": 0, "pid": 15216, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748254678697, "end": 1748254678698, "duration": 1, "pid": 15216, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748254678699, "end": 1748254678701, "duration": 2, "pid": 15216, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748254678702, "end": 1748254678703, "duration": 1, "pid": 15216, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748254678704, "end": 1748254678705, "duration": 1, "pid": 15216, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748254678706, "end": 1748254678706, "duration": 0, "pid": 15216, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748254678707, "end": 1748254678709, "duration": 2, "pid": 15216, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748254678710, "end": 1748254678711, "duration": 1, "pid": 15216, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748254678712, "end": 1748254678712, "duration": 0, "pid": 15216, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748254678715, "end": 1748254678715, "duration": 0, "pid": 15216, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748254678718, "end": 1748254678719, "duration": 1, "pid": 15216, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748254678723, "end": 1748254678723, "duration": 0, "pid": 15216, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748254678727, "end": 1748254678727, "duration": 0, "pid": 15216, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748254678736, "end": 1748254678740, "duration": 4, "pid": 15216, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748254678740, "end": 1748254678740, "duration": 0, "pid": 15216, "index": 30}, {"name": "Load extend/agent.js", "start": 1748254678743, "end": 1748254679153, "duration": 410, "pid": 15216, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/agent.js", "start": 1748254678744, "end": 1748254678748, "duration": 4, "pid": 15216, "index": 32}, {"name": "Require(28) node_modules/egg-schedule/app/extend/agent.js", "start": 1748254678755, "end": 1748254679126, "duration": 371, "pid": 15216, "index": 33}, {"name": "Require(29) node_modules/egg-logrotator/app/extend/agent.js", "start": 1748254679130, "end": 1748254679137, "duration": 7, "pid": 15216, "index": 34}, {"name": "Load extend/context.js", "start": 1748254679153, "end": 1748254679303, "duration": 150, "pid": 15216, "index": 35}, {"name": "Require(30) node_modules/egg-security/app/extend/context.js", "start": 1748254679154, "end": 1748254679181, "duration": 27, "pid": 15216, "index": 36}, {"name": "Require(31) node_modules/egg-jsonp/app/extend/context.js", "start": 1748254679182, "end": 1748254679188, "duration": 6, "pid": 15216, "index": 37}, {"name": "Require(32) node_modules/egg-i18n/app/extend/context.js", "start": 1748254679190, "end": 1748254679191, "duration": 1, "pid": 15216, "index": 38}, {"name": "Require(33) node_modules/egg-multipart/app/extend/context.js", "start": 1748254679193, "end": 1748254679257, "duration": 64, "pid": 15216, "index": 39}, {"name": "Require(34) node_modules/egg-view/app/extend/context.js", "start": 1748254679259, "end": 1748254679263, "duration": 4, "pid": 15216, "index": 40}, {"name": "Require(35) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748254679269, "end": 1748254679271, "duration": 2, "pid": 15216, "index": 41}, {"name": "Require(36) node_modules/egg/app/extend/context.js", "start": 1748254679272, "end": 1748254679279, "duration": 7, "pid": 15216, "index": 42}, {"name": "Load agent.js", "start": 1748254679304, "end": 1748254679486, "duration": 182, "pid": 15216, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1748254679310, "end": 1748254679313, "duration": 3, "pid": 15216, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1748254679314, "end": 1748254679315, "duration": 1, "pid": 15216, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1748254679319, "end": 1748254679360, "duration": 41, "pid": 15216, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1748254679361, "end": 1748254679364, "duration": 3, "pid": 15216, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1748254679365, "end": 1748254679430, "duration": 65, "pid": 15216, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1748254679431, "end": 1748254679431, "duration": 0, "pid": 15216, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1748254679436, "end": 1748254679436, "duration": 0, "pid": 15216, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1748254679439, "end": 1748254679471, "duration": 32, "pid": 15216, "index": 51}, {"name": "Require(45) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1748254679472, "end": 1748254679481, "duration": 9, "pid": 15216, "index": 52}, {"name": "Require(46) node_modules/egg/agent.js", "start": 1748254679482, "end": 1748254679485, "duration": 3, "pid": 15216, "index": 53}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748254679498, "end": 1748254680940, "duration": 1442, "pid": 15216, "index": 54}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1748254679499, "end": 1748254680891, "duration": 1392, "pid": 15216, "index": 55}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1748254679500, "end": 1748254681369, "duration": 1869, "pid": 15216, "index": 56}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748254680499, "end": 1748254680703, "duration": 204, "pid": 15216, "index": 57}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748254680544, "end": 1748254680930, "duration": 386, "pid": 15216, "index": 58}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748254680755, "end": 1748254681243, "duration": 488, "pid": 15216, "index": 59}]