[{"name": "Process Start", "start": 1748252514558, "end": 1748252516622, "duration": 2064, "pid": 16192, "index": 0}, {"name": "Application Start", "start": 1748252516624, "end": 1748252519304, "duration": 2680, "pid": 16192, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748252516677, "end": 1748252516785, "duration": 108, "pid": 16192, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748252516785, "end": 1748252516859, "duration": 74, "pid": 16192, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748252516787, "end": 1748252516790, "duration": 3, "pid": 16192, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748252516796, "end": 1748252516797, "duration": 1, "pid": 16192, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748252516799, "end": 1748252516800, "duration": 1, "pid": 16192, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748252516802, "end": 1748252516804, "duration": 2, "pid": 16192, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748252516807, "end": 1748252516808, "duration": 1, "pid": 16192, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748252516810, "end": 1748252516810, "duration": 0, "pid": 16192, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748252516811, "end": 1748252516812, "duration": 1, "pid": 16192, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748252516813, "end": 1748252516814, "duration": 1, "pid": 16192, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748252516814, "end": 1748252516816, "duration": 2, "pid": 16192, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748252516817, "end": 1748252516819, "duration": 2, "pid": 16192, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748252516820, "end": 1748252516821, "duration": 1, "pid": 16192, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748252516822, "end": 1748252516822, "duration": 0, "pid": 16192, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748252516823, "end": 1748252516824, "duration": 1, "pid": 16192, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748252516825, "end": 1748252516826, "duration": 1, "pid": 16192, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748252516827, "end": 1748252516827, "duration": 0, "pid": 16192, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748252516829, "end": 1748252516829, "duration": 0, "pid": 16192, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748252516830, "end": 1748252516831, "duration": 1, "pid": 16192, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748252516832, "end": 1748252516833, "duration": 1, "pid": 16192, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748252516834, "end": 1748252516834, "duration": 0, "pid": 16192, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748252516835, "end": 1748252516836, "duration": 1, "pid": 16192, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748252516836, "end": 1748252516837, "duration": 1, "pid": 16192, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748252516840, "end": 1748252516840, "duration": 0, "pid": 16192, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748252516843, "end": 1748252516843, "duration": 0, "pid": 16192, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748252516846, "end": 1748252516846, "duration": 0, "pid": 16192, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748252516850, "end": 1748252516850, "duration": 0, "pid": 16192, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748252516858, "end": 1748252516858, "duration": 0, "pid": 16192, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748252516859, "end": 1748252516859, "duration": 0, "pid": 16192, "index": 30}, {"name": "Load extend/application.js", "start": 1748252516861, "end": 1748252517027, "duration": 166, "pid": 16192, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748252516862, "end": 1748252516863, "duration": 1, "pid": 16192, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748252516864, "end": 1748252516868, "duration": 4, "pid": 16192, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748252516870, "end": 1748252516880, "duration": 10, "pid": 16192, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748252516882, "end": 1748252516893, "duration": 11, "pid": 16192, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748252516894, "end": 1748252516897, "duration": 3, "pid": 16192, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748252516899, "end": 1748252516902, "duration": 3, "pid": 16192, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748252516903, "end": 1748252517016, "duration": 113, "pid": 16192, "index": 38}, {"name": "Load extend/request.js", "start": 1748252517027, "end": 1748252517047, "duration": 20, "pid": 16192, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748252517035, "end": 1748252517037, "duration": 2, "pid": 16192, "index": 40}, {"name": "Load extend/response.js", "start": 1748252517047, "end": 1748252517075, "duration": 28, "pid": 16192, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748252517057, "end": 1748252517063, "duration": 6, "pid": 16192, "index": 42}, {"name": "Load extend/context.js", "start": 1748252517075, "end": 1748252517170, "duration": 95, "pid": 16192, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748252517076, "end": 1748252517096, "duration": 20, "pid": 16192, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748252517097, "end": 1748252517100, "duration": 3, "pid": 16192, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748252517102, "end": 1748252517102, "duration": 0, "pid": 16192, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748252517105, "end": 1748252517147, "duration": 42, "pid": 16192, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748252517149, "end": 1748252517151, "duration": 2, "pid": 16192, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748252517154, "end": 1748252517155, "duration": 1, "pid": 16192, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748252517156, "end": 1748252517160, "duration": 4, "pid": 16192, "index": 50}, {"name": "Load extend/helper.js", "start": 1748252517170, "end": 1748252517235, "duration": 65, "pid": 16192, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748252517172, "end": 1748252517218, "duration": 46, "pid": 16192, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748252517224, "end": 1748252517225, "duration": 1, "pid": 16192, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748252517225, "end": 1748252517226, "duration": 1, "pid": 16192, "index": 54}, {"name": "Load app.js", "start": 1748252517235, "end": 1748252517363, "duration": 128, "pid": 16192, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748252517236, "end": 1748252517237, "duration": 1, "pid": 16192, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748252517238, "end": 1748252517243, "duration": 5, "pid": 16192, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748252517246, "end": 1748252517272, "duration": 26, "pid": 16192, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748252517273, "end": 1748252517292, "duration": 19, "pid": 16192, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748252517293, "end": 1748252517313, "duration": 20, "pid": 16192, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748252517314, "end": 1748252517315, "duration": 1, "pid": 16192, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748252517316, "end": 1748252517319, "duration": 3, "pid": 16192, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748252517319, "end": 1748252517320, "duration": 1, "pid": 16192, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748252517320, "end": 1748252517321, "duration": 1, "pid": 16192, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748252517321, "end": 1748252517322, "duration": 1, "pid": 16192, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748252517325, "end": 1748252517325, "duration": 0, "pid": 16192, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748252517326, "end": 1748252517327, "duration": 1, "pid": 16192, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748252517328, "end": 1748252517329, "duration": 1, "pid": 16192, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748252517330, "end": 1748252517334, "duration": 4, "pid": 16192, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748252517335, "end": 1748252517354, "duration": 19, "pid": 16192, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748252517355, "end": 1748252517361, "duration": 6, "pid": 16192, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748252517376, "end": 1748252519288, "duration": 1912, "pid": 16192, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748252518330, "end": 1748252518419, "duration": 89, "pid": 16192, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748252518356, "end": 1748252519240, "duration": 884, "pid": 16192, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748252518452, "end": 1748252519303, "duration": 851, "pid": 16192, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748252518594, "end": 1748252519286, "duration": 692, "pid": 16192, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748252518713, "end": 1748252519257, "duration": 544, "pid": 16192, "index": 77}, {"name": "Load Service", "start": 1748252518713, "end": 1748252518886, "duration": 173, "pid": 16192, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748252518714, "end": 1748252518886, "duration": 172, "pid": 16192, "index": 79}, {"name": "Load Middleware", "start": 1748252518886, "end": 1748252519095, "duration": 209, "pid": 16192, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748252518887, "end": 1748252519076, "duration": 189, "pid": 16192, "index": 81}, {"name": "Load Controller", "start": 1748252519095, "end": 1748252519163, "duration": 68, "pid": 16192, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748252519095, "end": 1748252519163, "duration": 68, "pid": 16192, "index": 83}, {"name": "Load Router", "start": 1748252519163, "end": 1748252519172, "duration": 9, "pid": 16192, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748252519164, "end": 1748252519165, "duration": 1, "pid": 16192, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748252519167, "end": 1748252519240, "duration": 73, "pid": 16192, "index": 86}]