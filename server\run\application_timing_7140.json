[{"name": "Process Start", "start": 1748252917589, "end": 1748252919756, "duration": 2167, "pid": 7140, "index": 0}, {"name": "Application Start", "start": 1748252919761, "end": 1748252923986, "duration": 4225, "pid": 7140, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748252919787, "end": 1748252919834, "duration": 47, "pid": 7140, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748252919834, "end": 1748252919972, "duration": 138, "pid": 7140, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748252919835, "end": 1748252919837, "duration": 2, "pid": 7140, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748252919840, "end": 1748252919841, "duration": 1, "pid": 7140, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748252919842, "end": 1748252919843, "duration": 1, "pid": 7140, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748252919844, "end": 1748252919845, "duration": 1, "pid": 7140, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748252919846, "end": 1748252919847, "duration": 1, "pid": 7140, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748252919848, "end": 1748252919849, "duration": 1, "pid": 7140, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748252919850, "end": 1748252919850, "duration": 0, "pid": 7140, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748252919851, "end": 1748252919852, "duration": 1, "pid": 7140, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748252919853, "end": 1748252919854, "duration": 1, "pid": 7140, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748252919855, "end": 1748252919856, "duration": 1, "pid": 7140, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748252919857, "end": 1748252919857, "duration": 0, "pid": 7140, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748252919858, "end": 1748252919859, "duration": 1, "pid": 7140, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748252919860, "end": 1748252919860, "duration": 0, "pid": 7140, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748252919861, "end": 1748252919861, "duration": 0, "pid": 7140, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748252919862, "end": 1748252919863, "duration": 1, "pid": 7140, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748252919864, "end": 1748252919864, "duration": 0, "pid": 7140, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748252919865, "end": 1748252919866, "duration": 1, "pid": 7140, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748252919866, "end": 1748252919867, "duration": 1, "pid": 7140, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748252919869, "end": 1748252919869, "duration": 0, "pid": 7140, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748252919870, "end": 1748252919870, "duration": 0, "pid": 7140, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748252919871, "end": 1748252919871, "duration": 0, "pid": 7140, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748252919873, "end": 1748252919873, "duration": 0, "pid": 7140, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748252919875, "end": 1748252919875, "duration": 0, "pid": 7140, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748252919877, "end": 1748252919877, "duration": 0, "pid": 7140, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748252919881, "end": 1748252919882, "duration": 1, "pid": 7140, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748252919970, "end": 1748252919971, "duration": 1, "pid": 7140, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748252919972, "end": 1748252919972, "duration": 0, "pid": 7140, "index": 30}, {"name": "Load extend/application.js", "start": 1748252919976, "end": 1748252920143, "duration": 167, "pid": 7140, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748252919978, "end": 1748252919980, "duration": 2, "pid": 7140, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748252919981, "end": 1748252919984, "duration": 3, "pid": 7140, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748252919986, "end": 1748252919997, "duration": 11, "pid": 7140, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748252919999, "end": 1748252920017, "duration": 18, "pid": 7140, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748252920020, "end": 1748252920030, "duration": 10, "pid": 7140, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748252920033, "end": 1748252920039, "duration": 6, "pid": 7140, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748252920040, "end": 1748252920132, "duration": 92, "pid": 7140, "index": 38}, {"name": "Load extend/request.js", "start": 1748252920143, "end": 1748252920166, "duration": 23, "pid": 7140, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748252920151, "end": 1748252920154, "duration": 3, "pid": 7140, "index": 40}, {"name": "Load extend/response.js", "start": 1748252920166, "end": 1748252920195, "duration": 29, "pid": 7140, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748252920178, "end": 1748252920184, "duration": 6, "pid": 7140, "index": 42}, {"name": "Load extend/context.js", "start": 1748252920195, "end": 1748252920306, "duration": 111, "pid": 7140, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748252920198, "end": 1748252920223, "duration": 25, "pid": 7140, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748252920227, "end": 1748252920233, "duration": 6, "pid": 7140, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748252920234, "end": 1748252920235, "duration": 1, "pid": 7140, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748252920237, "end": 1748252920278, "duration": 41, "pid": 7140, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748252920280, "end": 1748252920283, "duration": 3, "pid": 7140, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748252920285, "end": 1748252920286, "duration": 1, "pid": 7140, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748252920288, "end": 1748252920293, "duration": 5, "pid": 7140, "index": 50}, {"name": "Load extend/helper.js", "start": 1748252920306, "end": 1748252920368, "duration": 62, "pid": 7140, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748252920308, "end": 1748252920338, "duration": 30, "pid": 7140, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748252920349, "end": 1748252920350, "duration": 1, "pid": 7140, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748252920353, "end": 1748252920354, "duration": 1, "pid": 7140, "index": 54}, {"name": "Load app.js", "start": 1748252920369, "end": 1748252920507, "duration": 138, "pid": 7140, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748252920369, "end": 1748252920370, "duration": 1, "pid": 7140, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748252920371, "end": 1748252920373, "duration": 2, "pid": 7140, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748252920376, "end": 1748252920400, "duration": 24, "pid": 7140, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748252920400, "end": 1748252920423, "duration": 23, "pid": 7140, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748252920424, "end": 1748252920448, "duration": 24, "pid": 7140, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748252920448, "end": 1748252920449, "duration": 1, "pid": 7140, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748252920450, "end": 1748252920453, "duration": 3, "pid": 7140, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748252920453, "end": 1748252920454, "duration": 1, "pid": 7140, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748252920455, "end": 1748252920455, "duration": 0, "pid": 7140, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748252920456, "end": 1748252920456, "duration": 0, "pid": 7140, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748252920457, "end": 1748252920460, "duration": 3, "pid": 7140, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748252920461, "end": 1748252920461, "duration": 0, "pid": 7140, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748252920462, "end": 1748252920462, "duration": 0, "pid": 7140, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748252920463, "end": 1748252920466, "duration": 3, "pid": 7140, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748252920467, "end": 1748252920498, "duration": 31, "pid": 7140, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748252920499, "end": 1748252920505, "duration": 6, "pid": 7140, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748252920528, "end": 1748252923949, "duration": 3421, "pid": 7140, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748252921731, "end": 1748252921833, "duration": 102, "pid": 7140, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748252921766, "end": 1748252923817, "duration": 2051, "pid": 7140, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748252921874, "end": 1748252923985, "duration": 2111, "pid": 7140, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748252922076, "end": 1748252923953, "duration": 1877, "pid": 7140, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748252922236, "end": 1748252923874, "duration": 1638, "pid": 7140, "index": 77}, {"name": "Load Service", "start": 1748252922236, "end": 1748252922602, "duration": 366, "pid": 7140, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748252922236, "end": 1748252922602, "duration": 366, "pid": 7140, "index": 79}, {"name": "Load Middleware", "start": 1748252922602, "end": 1748252923382, "duration": 780, "pid": 7140, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748252922602, "end": 1748252923289, "duration": 687, "pid": 7140, "index": 81}, {"name": "Load Controller", "start": 1748252923382, "end": 1748252923522, "duration": 140, "pid": 7140, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748252923382, "end": 1748252923522, "duration": 140, "pid": 7140, "index": 83}, {"name": "Load Router", "start": 1748252923522, "end": 1748252923536, "duration": 14, "pid": 7140, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748252923523, "end": 1748252923525, "duration": 2, "pid": 7140, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748252923527, "end": 1748252923817, "duration": 290, "pid": 7140, "index": 86}]