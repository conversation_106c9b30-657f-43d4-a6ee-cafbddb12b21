[{"name": "Process Start", "start": 1748253134231, "end": 1748253137211, "duration": 2980, "pid": 29312, "index": 0}, {"name": "Application Start", "start": 1748253137216, "end": 1748253140925, "duration": 3709, "pid": 29312, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748253137269, "end": 1748253137336, "duration": 67, "pid": 29312, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748253137337, "end": 1748253137478, "duration": 141, "pid": 29312, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748253137339, "end": 1748253137340, "duration": 1, "pid": 29312, "index": 4}, {"name": "Require(1) config/config.prod.js", "start": 1748253137344, "end": 1748253137344, "duration": 0, "pid": 29312, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748253137345, "end": 1748253137346, "duration": 1, "pid": 29312, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748253137347, "end": 1748253137348, "duration": 1, "pid": 29312, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748253137367, "end": 1748253137369, "duration": 2, "pid": 29312, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748253137372, "end": 1748253137373, "duration": 1, "pid": 29312, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748253137375, "end": 1748253137376, "duration": 1, "pid": 29312, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748253137377, "end": 1748253137378, "duration": 1, "pid": 29312, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748253137379, "end": 1748253137380, "duration": 1, "pid": 29312, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748253137382, "end": 1748253137383, "duration": 1, "pid": 29312, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1748253137384, "end": 1748253137385, "duration": 1, "pid": 29312, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1748253137388, "end": 1748253137389, "duration": 1, "pid": 29312, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1748253137391, "end": 1748253137391, "duration": 0, "pid": 29312, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1748253137393, "end": 1748253137393, "duration": 0, "pid": 29312, "index": 17}, {"name": "Require(14) node_modules/egg-jwt/config/config.default.js", "start": 1748253137395, "end": 1748253137396, "duration": 1, "pid": 29312, "index": 18}, {"name": "Require(15) node_modules/egg-cors/config/config.default.js", "start": 1748253137399, "end": 1748253137400, "duration": 1, "pid": 29312, "index": 19}, {"name": "Require(16) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748253137402, "end": 1748253137444, "duration": 42, "pid": 29312, "index": 20}, {"name": "Require(17) node_modules/egg-mysql/config/config.default.js", "start": 1748253137446, "end": 1748253137447, "duration": 1, "pid": 29312, "index": 21}, {"name": "Require(18) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748253137448, "end": 1748253137449, "duration": 1, "pid": 29312, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748253137450, "end": 1748253137452, "duration": 2, "pid": 29312, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748253137456, "end": 1748253137456, "duration": 0, "pid": 29312, "index": 24}, {"name": "Require(21) node_modules/egg-static/config/config.prod.js", "start": 1748253137466, "end": 1748253137467, "duration": 1, "pid": 29312, "index": 25}, {"name": "Require(22) config/config.prod.js", "start": 1748253137478, "end": 1748253137478, "duration": 0, "pid": 29312, "index": 26}, {"name": "Load extend/application.js", "start": 1748253137481, "end": 1748253137770, "duration": 289, "pid": 29312, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1748253137482, "end": 1748253137483, "duration": 1, "pid": 29312, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1748253137485, "end": 1748253137498, "duration": 13, "pid": 29312, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1748253137500, "end": 1748253137515, "duration": 15, "pid": 29312, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1748253137518, "end": 1748253137533, "duration": 15, "pid": 29312, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1748253137534, "end": 1748253137542, "duration": 8, "pid": 29312, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1748253137544, "end": 1748253137546, "duration": 2, "pid": 29312, "index": 33}, {"name": "Require(29) node_modules/egg-jwt/app/extend/application.js", "start": 1748253137554, "end": 1748253137738, "duration": 184, "pid": 29312, "index": 34}, {"name": "Load extend/request.js", "start": 1748253137770, "end": 1748253137815, "duration": 45, "pid": 29312, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1748253137792, "end": 1748253137794, "duration": 2, "pid": 29312, "index": 36}, {"name": "Load extend/response.js", "start": 1748253137815, "end": 1748253137860, "duration": 45, "pid": 29312, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1748253137830, "end": 1748253137841, "duration": 11, "pid": 29312, "index": 38}, {"name": "Load extend/context.js", "start": 1748253137860, "end": 1748253138081, "duration": 221, "pid": 29312, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1748253137863, "end": 1748253137931, "duration": 68, "pid": 29312, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1748253137932, "end": 1748253137938, "duration": 6, "pid": 29312, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1748253137940, "end": 1748253137941, "duration": 1, "pid": 29312, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1748253137943, "end": 1748253138038, "duration": 95, "pid": 29312, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1748253138043, "end": 1748253138045, "duration": 2, "pid": 29312, "index": 44}, {"name": "Require(37) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748253138048, "end": 1748253138049, "duration": 1, "pid": 29312, "index": 45}, {"name": "Require(38) node_modules/egg/app/extend/context.js", "start": 1748253138050, "end": 1748253138062, "duration": 12, "pid": 29312, "index": 46}, {"name": "Load extend/helper.js", "start": 1748253138081, "end": 1748253138181, "duration": 100, "pid": 29312, "index": 47}, {"name": "Require(39) node_modules/egg-security/app/extend/helper.js", "start": 1748253138083, "end": 1748253138145, "duration": 62, "pid": 29312, "index": 48}, {"name": "Require(40) node_modules/egg/app/extend/helper.js", "start": 1748253138160, "end": 1748253138161, "duration": 1, "pid": 29312, "index": 49}, {"name": "Require(41) app/extend/helper.js", "start": 1748253138162, "end": 1748253138163, "duration": 1, "pid": 29312, "index": 50}, {"name": "Load app.js", "start": 1748253138181, "end": 1748253138326, "duration": 145, "pid": 29312, "index": 51}, {"name": "Require(42) node_modules/egg-session/app.js", "start": 1748253138182, "end": 1748253138182, "duration": 0, "pid": 29312, "index": 52}, {"name": "Require(43) node_modules/egg-security/app.js", "start": 1748253138183, "end": 1748253138187, "duration": 4, "pid": 29312, "index": 53}, {"name": "Require(44) node_modules/egg-onerror/app.js", "start": 1748253138189, "end": 1748253138209, "duration": 20, "pid": 29312, "index": 54}, {"name": "Require(45) node_modules/egg-i18n/app.js", "start": 1748253138210, "end": 1748253138238, "duration": 28, "pid": 29312, "index": 55}, {"name": "Require(46) node_modules/egg-watcher/app.js", "start": 1748253138239, "end": 1748253138261, "duration": 22, "pid": 29312, "index": 56}, {"name": "Require(47) node_modules/egg-schedule/app.js", "start": 1748253138261, "end": 1748253138263, "duration": 2, "pid": 29312, "index": 57}, {"name": "Require(48) node_modules/egg-multipart/app.js", "start": 1748253138263, "end": 1748253138266, "duration": 3, "pid": 29312, "index": 58}, {"name": "Require(49) node_modules/egg-logrotator/app.js", "start": 1748253138267, "end": 1748253138268, "duration": 1, "pid": 29312, "index": 59}, {"name": "Require(50) node_modules/egg-static/app.js", "start": 1748253138269, "end": 1748253138269, "duration": 0, "pid": 29312, "index": 60}, {"name": "Require(51) node_modules/egg-sequelize/app.js", "start": 1748253138271, "end": 1748253138271, "duration": 0, "pid": 29312, "index": 61}, {"name": "Require(52) node_modules/egg-jwt/app.js", "start": 1748253138272, "end": 1748253138273, "duration": 1, "pid": 29312, "index": 62}, {"name": "Require(53) node_modules/egg-cors/app.js", "start": 1748253138273, "end": 1748253138274, "duration": 1, "pid": 29312, "index": 63}, {"name": "Require(54) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748253138275, "end": 1748253138278, "duration": 3, "pid": 29312, "index": 64}, {"name": "Require(55) node_modules/egg-mysql/app.js", "start": 1748253138279, "end": 1748253138313, "duration": 34, "pid": 29312, "index": 65}, {"name": "Require(56) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748253138314, "end": 1748253138324, "duration": 10, "pid": 29312, "index": 66}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748253138347, "end": 1748253140907, "duration": 2560, "pid": 29312, "index": 67}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748253139786, "end": 1748253139912, "duration": 126, "pid": 29312, "index": 68}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748253139819, "end": 1748253140799, "duration": 980, "pid": 29312, "index": 69}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748253139956, "end": 1748253140925, "duration": 969, "pid": 29312, "index": 70}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748253140112, "end": 1748253140899, "duration": 787, "pid": 29312, "index": 71}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748253140234, "end": 1748253140849, "duration": 615, "pid": 29312, "index": 72}, {"name": "Load Service", "start": 1748253140235, "end": 1748253140409, "duration": 174, "pid": 29312, "index": 73}, {"name": "Load \"service\" to Context", "start": 1748253140235, "end": 1748253140409, "duration": 174, "pid": 29312, "index": 74}, {"name": "Load Middleware", "start": 1748253140409, "end": 1748253140637, "duration": 228, "pid": 29312, "index": 75}, {"name": "Load \"middlewares\" to Application", "start": 1748253140409, "end": 1748253140607, "duration": 198, "pid": 29312, "index": 76}, {"name": "Load Controller", "start": 1748253140638, "end": 1748253140709, "duration": 71, "pid": 29312, "index": 77}, {"name": "Load \"controller\" to Application", "start": 1748253140638, "end": 1748253140709, "duration": 71, "pid": 29312, "index": 78}, {"name": "Load Router", "start": 1748253140709, "end": 1748253140717, "duration": 8, "pid": 29312, "index": 79}, {"name": "Require(57) app/router.js", "start": 1748253140710, "end": 1748253140710, "duration": 0, "pid": 29312, "index": 80}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748253140712, "end": 1748253140799, "duration": 87, "pid": 29312, "index": 81}]