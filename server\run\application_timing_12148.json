[{"name": "Process Start", "start": 1748253133623, "end": 1748253136684, "duration": 3061, "pid": 12148, "index": 0}, {"name": "Application Start", "start": 1748253136686, "end": 1748253140480, "duration": 3794, "pid": 12148, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748253136722, "end": 1748253136788, "duration": 66, "pid": 12148, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748253136788, "end": 1748253136862, "duration": 74, "pid": 12148, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748253136790, "end": 1748253136790, "duration": 0, "pid": 12148, "index": 4}, {"name": "Require(1) config/config.prod.js", "start": 1748253136794, "end": 1748253136794, "duration": 0, "pid": 12148, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748253136796, "end": 1748253136796, "duration": 0, "pid": 12148, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748253136797, "end": 1748253136798, "duration": 1, "pid": 12148, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748253136800, "end": 1748253136801, "duration": 1, "pid": 12148, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748253136802, "end": 1748253136803, "duration": 1, "pid": 12148, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748253136804, "end": 1748253136805, "duration": 1, "pid": 12148, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748253136806, "end": 1748253136807, "duration": 1, "pid": 12148, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748253136808, "end": 1748253136808, "duration": 0, "pid": 12148, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748253136810, "end": 1748253136810, "duration": 0, "pid": 12148, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1748253136812, "end": 1748253136812, "duration": 0, "pid": 12148, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1748253136818, "end": 1748253136825, "duration": 7, "pid": 12148, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1748253136826, "end": 1748253136827, "duration": 1, "pid": 12148, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1748253136828, "end": 1748253136829, "duration": 1, "pid": 12148, "index": 17}, {"name": "Require(14) node_modules/egg-jwt/config/config.default.js", "start": 1748253136830, "end": 1748253136831, "duration": 1, "pid": 12148, "index": 18}, {"name": "Require(15) node_modules/egg-cors/config/config.default.js", "start": 1748253136833, "end": 1748253136833, "duration": 0, "pid": 12148, "index": 19}, {"name": "Require(16) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748253136838, "end": 1748253136839, "duration": 1, "pid": 12148, "index": 20}, {"name": "Require(17) node_modules/egg-mysql/config/config.default.js", "start": 1748253136840, "end": 1748253136841, "duration": 1, "pid": 12148, "index": 21}, {"name": "Require(18) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748253136843, "end": 1748253136844, "duration": 1, "pid": 12148, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748253136845, "end": 1748253136845, "duration": 0, "pid": 12148, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748253136847, "end": 1748253136847, "duration": 0, "pid": 12148, "index": 24}, {"name": "Require(21) node_modules/egg-static/config/config.prod.js", "start": 1748253136856, "end": 1748253136856, "duration": 0, "pid": 12148, "index": 25}, {"name": "Require(22) config/config.prod.js", "start": 1748253136862, "end": 1748253136862, "duration": 0, "pid": 12148, "index": 26}, {"name": "Load extend/application.js", "start": 1748253136866, "end": 1748253137058, "duration": 192, "pid": 12148, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1748253136867, "end": 1748253136869, "duration": 2, "pid": 12148, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1748253136870, "end": 1748253136873, "duration": 3, "pid": 12148, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1748253136875, "end": 1748253136886, "duration": 11, "pid": 12148, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1748253136889, "end": 1748253136903, "duration": 14, "pid": 12148, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1748253136906, "end": 1748253136909, "duration": 3, "pid": 12148, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1748253136911, "end": 1748253136915, "duration": 4, "pid": 12148, "index": 33}, {"name": "Require(29) node_modules/egg-jwt/app/extend/application.js", "start": 1748253136917, "end": 1748253137046, "duration": 129, "pid": 12148, "index": 34}, {"name": "Load extend/request.js", "start": 1748253137058, "end": 1748253137093, "duration": 35, "pid": 12148, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1748253137076, "end": 1748253137080, "duration": 4, "pid": 12148, "index": 36}, {"name": "Load extend/response.js", "start": 1748253137093, "end": 1748253137138, "duration": 45, "pid": 12148, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1748253137105, "end": 1748253137111, "duration": 6, "pid": 12148, "index": 38}, {"name": "Load extend/context.js", "start": 1748253137138, "end": 1748253137261, "duration": 123, "pid": 12148, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1748253137140, "end": 1748253137172, "duration": 32, "pid": 12148, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1748253137174, "end": 1748253137179, "duration": 5, "pid": 12148, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1748253137181, "end": 1748253137181, "duration": 0, "pid": 12148, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1748253137185, "end": 1748253137230, "duration": 45, "pid": 12148, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1748253137232, "end": 1748253137234, "duration": 2, "pid": 12148, "index": 44}, {"name": "Require(37) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748253137238, "end": 1748253137239, "duration": 1, "pid": 12148, "index": 45}, {"name": "Require(38) node_modules/egg/app/extend/context.js", "start": 1748253137241, "end": 1748253137247, "duration": 6, "pid": 12148, "index": 46}, {"name": "Load extend/helper.js", "start": 1748253137261, "end": 1748253137330, "duration": 69, "pid": 12148, "index": 47}, {"name": "Require(39) node_modules/egg-security/app/extend/helper.js", "start": 1748253137263, "end": 1748253137307, "duration": 44, "pid": 12148, "index": 48}, {"name": "Require(40) node_modules/egg/app/extend/helper.js", "start": 1748253137315, "end": 1748253137316, "duration": 1, "pid": 12148, "index": 49}, {"name": "Require(41) app/extend/helper.js", "start": 1748253137316, "end": 1748253137317, "duration": 1, "pid": 12148, "index": 50}, {"name": "Load app.js", "start": 1748253137330, "end": 1748253137659, "duration": 329, "pid": 12148, "index": 51}, {"name": "Require(42) node_modules/egg-session/app.js", "start": 1748253137333, "end": 1748253137340, "duration": 7, "pid": 12148, "index": 52}, {"name": "Require(43) node_modules/egg-security/app.js", "start": 1748253137340, "end": 1748253137344, "duration": 4, "pid": 12148, "index": 53}, {"name": "Require(44) node_modules/egg-onerror/app.js", "start": 1748253137346, "end": 1748253137404, "duration": 58, "pid": 12148, "index": 54}, {"name": "Require(45) node_modules/egg-i18n/app.js", "start": 1748253137406, "end": 1748253137475, "duration": 69, "pid": 12148, "index": 55}, {"name": "Require(46) node_modules/egg-watcher/app.js", "start": 1748253137477, "end": 1748253137510, "duration": 33, "pid": 12148, "index": 56}, {"name": "Require(47) node_modules/egg-schedule/app.js", "start": 1748253137510, "end": 1748253137512, "duration": 2, "pid": 12148, "index": 57}, {"name": "Require(48) node_modules/egg-multipart/app.js", "start": 1748253137512, "end": 1748253137517, "duration": 5, "pid": 12148, "index": 58}, {"name": "Require(49) node_modules/egg-logrotator/app.js", "start": 1748253137520, "end": 1748253137520, "duration": 0, "pid": 12148, "index": 59}, {"name": "Require(50) node_modules/egg-static/app.js", "start": 1748253137522, "end": 1748253137524, "duration": 2, "pid": 12148, "index": 60}, {"name": "Require(51) node_modules/egg-sequelize/app.js", "start": 1748253137526, "end": 1748253137526, "duration": 0, "pid": 12148, "index": 61}, {"name": "Require(52) node_modules/egg-jwt/app.js", "start": 1748253137527, "end": 1748253137528, "duration": 1, "pid": 12148, "index": 62}, {"name": "Require(53) node_modules/egg-cors/app.js", "start": 1748253137529, "end": 1748253137529, "duration": 0, "pid": 12148, "index": 63}, {"name": "Require(54) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748253137530, "end": 1748253137534, "duration": 4, "pid": 12148, "index": 64}, {"name": "Require(55) node_modules/egg-mysql/app.js", "start": 1748253137539, "end": 1748253137643, "duration": 104, "pid": 12148, "index": 65}, {"name": "Require(56) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748253137644, "end": 1748253137657, "duration": 13, "pid": 12148, "index": 66}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748253137728, "end": 1748253140460, "duration": 2732, "pid": 12148, "index": 67}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748253139108, "end": 1748253139292, "duration": 184, "pid": 12148, "index": 68}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748253139157, "end": 1748253140388, "duration": 1231, "pid": 12148, "index": 69}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748253139389, "end": 1748253140480, "duration": 1091, "pid": 12148, "index": 70}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748253139673, "end": 1748253140462, "duration": 789, "pid": 12148, "index": 71}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748253139854, "end": 1748253140417, "duration": 563, "pid": 12148, "index": 72}, {"name": "Load Service", "start": 1748253139855, "end": 1748253140063, "duration": 208, "pid": 12148, "index": 73}, {"name": "Load \"service\" to Context", "start": 1748253139855, "end": 1748253140063, "duration": 208, "pid": 12148, "index": 74}, {"name": "Load Middleware", "start": 1748253140064, "end": 1748253140263, "duration": 199, "pid": 12148, "index": 75}, {"name": "Load \"middlewares\" to Application", "start": 1748253140064, "end": 1748253140247, "duration": 183, "pid": 12148, "index": 76}, {"name": "Load Controller", "start": 1748253140263, "end": 1748253140313, "duration": 50, "pid": 12148, "index": 77}, {"name": "Load \"controller\" to Application", "start": 1748253140263, "end": 1748253140313, "duration": 50, "pid": 12148, "index": 78}, {"name": "Load Router", "start": 1748253140313, "end": 1748253140322, "duration": 9, "pid": 12148, "index": 79}, {"name": "Require(57) app/router.js", "start": 1748253140313, "end": 1748253140314, "duration": 1, "pid": 12148, "index": 80}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748253140315, "end": 1748253140388, "duration": 73, "pid": 12148, "index": 81}]