[{"name": "Process Start", "start": 1748253035227, "end": 1748253038929, "duration": 3702, "pid": 1608, "index": 0}, {"name": "Application Start", "start": 1748253038935, "end": 1748253048044, "duration": 9109, "pid": 1608, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748253039001, "end": 1748253039095, "duration": 94, "pid": 1608, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748253039095, "end": 1748253039269, "duration": 174, "pid": 1608, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748253039096, "end": 1748253039097, "duration": 1, "pid": 1608, "index": 4}, {"name": "Require(1) config/config.prod.js", "start": 1748253039101, "end": 1748253039107, "duration": 6, "pid": 1608, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748253039109, "end": 1748253039109, "duration": 0, "pid": 1608, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748253039110, "end": 1748253039111, "duration": 1, "pid": 1608, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748253039114, "end": 1748253039115, "duration": 1, "pid": 1608, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748253039116, "end": 1748253039117, "duration": 1, "pid": 1608, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748253039119, "end": 1748253039120, "duration": 1, "pid": 1608, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748253039122, "end": 1748253039145, "duration": 23, "pid": 1608, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748253039146, "end": 1748253039147, "duration": 1, "pid": 1608, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748253039148, "end": 1748253039149, "duration": 1, "pid": 1608, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1748253039152, "end": 1748253039153, "duration": 1, "pid": 1608, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1748253039154, "end": 1748253039156, "duration": 2, "pid": 1608, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1748253039158, "end": 1748253039158, "duration": 0, "pid": 1608, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1748253039160, "end": 1748253039161, "duration": 1, "pid": 1608, "index": 17}, {"name": "Require(14) node_modules/egg-jwt/config/config.default.js", "start": 1748253039162, "end": 1748253039163, "duration": 1, "pid": 1608, "index": 18}, {"name": "Require(15) node_modules/egg-cors/config/config.default.js", "start": 1748253039228, "end": 1748253039229, "duration": 1, "pid": 1608, "index": 19}, {"name": "Require(16) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748253039229, "end": 1748253039230, "duration": 1, "pid": 1608, "index": 20}, {"name": "Require(17) node_modules/egg-mysql/config/config.default.js", "start": 1748253039231, "end": 1748253039233, "duration": 2, "pid": 1608, "index": 21}, {"name": "Require(18) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748253039233, "end": 1748253039234, "duration": 1, "pid": 1608, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748253039235, "end": 1748253039236, "duration": 1, "pid": 1608, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748253039240, "end": 1748253039240, "duration": 0, "pid": 1608, "index": 24}, {"name": "Require(21) node_modules/egg-static/config/config.prod.js", "start": 1748253039256, "end": 1748253039256, "duration": 0, "pid": 1608, "index": 25}, {"name": "Require(22) config/config.prod.js", "start": 1748253039269, "end": 1748253039269, "duration": 0, "pid": 1608, "index": 26}, {"name": "Load extend/application.js", "start": 1748253039278, "end": 1748253040329, "duration": 1051, "pid": 1608, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1748253039280, "end": 1748253039281, "duration": 1, "pid": 1608, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1748253039283, "end": 1748253039288, "duration": 5, "pid": 1608, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1748253039289, "end": 1748253039315, "duration": 26, "pid": 1608, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1748253039323, "end": 1748253039368, "duration": 45, "pid": 1608, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1748253039370, "end": 1748253039373, "duration": 3, "pid": 1608, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1748253039374, "end": 1748253039378, "duration": 4, "pid": 1608, "index": 33}, {"name": "Require(29) node_modules/egg-jwt/app/extend/application.js", "start": 1748253039380, "end": 1748253040033, "duration": 653, "pid": 1608, "index": 34}, {"name": "Load extend/request.js", "start": 1748253040329, "end": 1748253040366, "duration": 37, "pid": 1608, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1748253040344, "end": 1748253040346, "duration": 2, "pid": 1608, "index": 36}, {"name": "Load extend/response.js", "start": 1748253040366, "end": 1748253040414, "duration": 48, "pid": 1608, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1748253040379, "end": 1748253040386, "duration": 7, "pid": 1608, "index": 38}, {"name": "Load extend/context.js", "start": 1748253040414, "end": 1748253040653, "duration": 239, "pid": 1608, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1748253040416, "end": 1748253040524, "duration": 108, "pid": 1608, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1748253040525, "end": 1748253040528, "duration": 3, "pid": 1608, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1748253040530, "end": 1748253040531, "duration": 1, "pid": 1608, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1748253040533, "end": 1748253040625, "duration": 92, "pid": 1608, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1748253040628, "end": 1748253040630, "duration": 2, "pid": 1608, "index": 44}, {"name": "Require(37) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748253040632, "end": 1748253040633, "duration": 1, "pid": 1608, "index": 45}, {"name": "Require(38) node_modules/egg/app/extend/context.js", "start": 1748253040634, "end": 1748253040640, "duration": 6, "pid": 1608, "index": 46}, {"name": "Load extend/helper.js", "start": 1748253040653, "end": 1748253041021, "duration": 368, "pid": 1608, "index": 47}, {"name": "Require(39) node_modules/egg-security/app/extend/helper.js", "start": 1748253040655, "end": 1748253040909, "duration": 254, "pid": 1608, "index": 48}, {"name": "Require(40) node_modules/egg/app/extend/helper.js", "start": 1748253040936, "end": 1748253040937, "duration": 1, "pid": 1608, "index": 49}, {"name": "Require(41) app/extend/helper.js", "start": 1748253040940, "end": 1748253040941, "duration": 1, "pid": 1608, "index": 50}, {"name": "Load app.js", "start": 1748253041021, "end": 1748253041337, "duration": 316, "pid": 1608, "index": 51}, {"name": "Require(42) node_modules/egg-session/app.js", "start": 1748253041022, "end": 1748253041023, "duration": 1, "pid": 1608, "index": 52}, {"name": "Require(43) node_modules/egg-security/app.js", "start": 1748253041025, "end": 1748253041035, "duration": 10, "pid": 1608, "index": 53}, {"name": "Require(44) node_modules/egg-onerror/app.js", "start": 1748253041038, "end": 1748253041112, "duration": 74, "pid": 1608, "index": 54}, {"name": "Require(45) node_modules/egg-i18n/app.js", "start": 1748253041113, "end": 1748253041149, "duration": 36, "pid": 1608, "index": 55}, {"name": "Require(46) node_modules/egg-watcher/app.js", "start": 1748253041150, "end": 1748253041193, "duration": 43, "pid": 1608, "index": 56}, {"name": "Require(47) node_modules/egg-schedule/app.js", "start": 1748253041193, "end": 1748253041195, "duration": 2, "pid": 1608, "index": 57}, {"name": "Require(48) node_modules/egg-multipart/app.js", "start": 1748253041197, "end": 1748253041209, "duration": 12, "pid": 1608, "index": 58}, {"name": "Require(49) node_modules/egg-logrotator/app.js", "start": 1748253041209, "end": 1748253041210, "duration": 1, "pid": 1608, "index": 59}, {"name": "Require(50) node_modules/egg-static/app.js", "start": 1748253041211, "end": 1748253041211, "duration": 0, "pid": 1608, "index": 60}, {"name": "Require(51) node_modules/egg-sequelize/app.js", "start": 1748253041227, "end": 1748253041228, "duration": 1, "pid": 1608, "index": 61}, {"name": "Require(52) node_modules/egg-jwt/app.js", "start": 1748253041229, "end": 1748253041229, "duration": 0, "pid": 1608, "index": 62}, {"name": "Require(53) node_modules/egg-cors/app.js", "start": 1748253041230, "end": 1748253041231, "duration": 1, "pid": 1608, "index": 63}, {"name": "Require(54) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748253041231, "end": 1748253041241, "duration": 10, "pid": 1608, "index": 64}, {"name": "Require(55) node_modules/egg-mysql/app.js", "start": 1748253041246, "end": 1748253041323, "duration": 77, "pid": 1608, "index": 65}, {"name": "Require(56) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748253041324, "end": 1748253041333, "duration": 9, "pid": 1608, "index": 66}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748253041365, "end": 1748253048033, "duration": 6668, "pid": 1608, "index": 67}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748253044148, "end": 1748253044370, "duration": 222, "pid": 1608, "index": 68}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748253044207, "end": 1748253047614, "duration": 3407, "pid": 1608, "index": 69}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748253044482, "end": 1748253048042, "duration": 3560, "pid": 1608, "index": 70}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748253044789, "end": 1748253047999, "duration": 3210, "pid": 1608, "index": 71}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748253045132, "end": 1748253047766, "duration": 2634, "pid": 1608, "index": 72}, {"name": "Load Service", "start": 1748253045132, "end": 1748253045822, "duration": 690, "pid": 1608, "index": 73}, {"name": "Load \"service\" to Context", "start": 1748253045133, "end": 1748253045821, "duration": 688, "pid": 1608, "index": 74}, {"name": "Load Middleware", "start": 1748253045822, "end": 1748253046467, "duration": 645, "pid": 1608, "index": 75}, {"name": "Load \"middlewares\" to Application", "start": 1748253045823, "end": 1748253046382, "duration": 559, "pid": 1608, "index": 76}, {"name": "Load Controller", "start": 1748253046470, "end": 1748253047175, "duration": 705, "pid": 1608, "index": 77}, {"name": "Load \"controller\" to Application", "start": 1748253046471, "end": 1748253047175, "duration": 704, "pid": 1608, "index": 78}, {"name": "Load Router", "start": 1748253047175, "end": 1748253047217, "duration": 42, "pid": 1608, "index": 79}, {"name": "Require(57) app/router.js", "start": 1748253047180, "end": 1748253047183, "duration": 3, "pid": 1608, "index": 80}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748253047190, "end": 1748253047613, "duration": 423, "pid": 1608, "index": 81}]