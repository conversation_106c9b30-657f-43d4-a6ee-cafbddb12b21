[{"name": "Process Start", "start": 1748254681653, "end": 1748254684162, "duration": 2509, "pid": 2304, "index": 0}, {"name": "Application Start", "start": 1748254684163, "end": 1748254687672, "duration": 3509, "pid": 2304, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748254684188, "end": 1748254684237, "duration": 49, "pid": 2304, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748254684237, "end": 1748254684301, "duration": 64, "pid": 2304, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748254684238, "end": 1748254684239, "duration": 1, "pid": 2304, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748254684242, "end": 1748254684242, "duration": 0, "pid": 2304, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748254684243, "end": 1748254684244, "duration": 1, "pid": 2304, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748254684244, "end": 1748254684245, "duration": 1, "pid": 2304, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748254684248, "end": 1748254684249, "duration": 1, "pid": 2304, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748254684251, "end": 1748254684252, "duration": 1, "pid": 2304, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748254684254, "end": 1748254684254, "duration": 0, "pid": 2304, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748254684256, "end": 1748254684257, "duration": 1, "pid": 2304, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748254684258, "end": 1748254684258, "duration": 0, "pid": 2304, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748254684260, "end": 1748254684261, "duration": 1, "pid": 2304, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748254684263, "end": 1748254684264, "duration": 1, "pid": 2304, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748254684265, "end": 1748254684267, "duration": 2, "pid": 2304, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748254684269, "end": 1748254684270, "duration": 1, "pid": 2304, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748254684271, "end": 1748254684271, "duration": 0, "pid": 2304, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748254684272, "end": 1748254684273, "duration": 1, "pid": 2304, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748254684274, "end": 1748254684274, "duration": 0, "pid": 2304, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748254684275, "end": 1748254684276, "duration": 1, "pid": 2304, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748254684277, "end": 1748254684278, "duration": 1, "pid": 2304, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748254684278, "end": 1748254684279, "duration": 1, "pid": 2304, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748254684280, "end": 1748254684281, "duration": 1, "pid": 2304, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748254684281, "end": 1748254684282, "duration": 1, "pid": 2304, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748254684285, "end": 1748254684285, "duration": 0, "pid": 2304, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748254684286, "end": 1748254684287, "duration": 1, "pid": 2304, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748254684289, "end": 1748254684290, "duration": 1, "pid": 2304, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748254684293, "end": 1748254684293, "duration": 0, "pid": 2304, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748254684298, "end": 1748254684300, "duration": 2, "pid": 2304, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748254684300, "end": 1748254684300, "duration": 0, "pid": 2304, "index": 30}, {"name": "Load extend/application.js", "start": 1748254684302, "end": 1748254684564, "duration": 262, "pid": 2304, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748254684304, "end": 1748254684305, "duration": 1, "pid": 2304, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748254684306, "end": 1748254684309, "duration": 3, "pid": 2304, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748254684310, "end": 1748254684323, "duration": 13, "pid": 2304, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748254684324, "end": 1748254684336, "duration": 12, "pid": 2304, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748254684342, "end": 1748254684373, "duration": 31, "pid": 2304, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748254684375, "end": 1748254684388, "duration": 13, "pid": 2304, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748254684393, "end": 1748254684554, "duration": 161, "pid": 2304, "index": 38}, {"name": "Load extend/request.js", "start": 1748254684564, "end": 1748254684584, "duration": 20, "pid": 2304, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748254684572, "end": 1748254684575, "duration": 3, "pid": 2304, "index": 40}, {"name": "Load extend/response.js", "start": 1748254684584, "end": 1748254684609, "duration": 25, "pid": 2304, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748254684594, "end": 1748254684599, "duration": 5, "pid": 2304, "index": 42}, {"name": "Load extend/context.js", "start": 1748254684609, "end": 1748254684711, "duration": 102, "pid": 2304, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748254684610, "end": 1748254684640, "duration": 30, "pid": 2304, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748254684641, "end": 1748254684644, "duration": 3, "pid": 2304, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748254684645, "end": 1748254684646, "duration": 1, "pid": 2304, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748254684648, "end": 1748254684687, "duration": 39, "pid": 2304, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748254684689, "end": 1748254684691, "duration": 2, "pid": 2304, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748254684692, "end": 1748254684693, "duration": 1, "pid": 2304, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748254684694, "end": 1748254684697, "duration": 3, "pid": 2304, "index": 50}, {"name": "Load extend/helper.js", "start": 1748254684711, "end": 1748254684774, "duration": 63, "pid": 2304, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748254684712, "end": 1748254684754, "duration": 42, "pid": 2304, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748254684762, "end": 1748254684763, "duration": 1, "pid": 2304, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748254684764, "end": 1748254684765, "duration": 1, "pid": 2304, "index": 54}, {"name": "Load app.js", "start": 1748254684775, "end": 1748254684964, "duration": 189, "pid": 2304, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748254684775, "end": 1748254684776, "duration": 1, "pid": 2304, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748254684777, "end": 1748254684782, "duration": 5, "pid": 2304, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748254684784, "end": 1748254684816, "duration": 32, "pid": 2304, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748254684817, "end": 1748254684861, "duration": 44, "pid": 2304, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748254684862, "end": 1748254684887, "duration": 25, "pid": 2304, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748254684889, "end": 1748254684891, "duration": 2, "pid": 2304, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748254684892, "end": 1748254684895, "duration": 3, "pid": 2304, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748254684896, "end": 1748254684896, "duration": 0, "pid": 2304, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748254684897, "end": 1748254684898, "duration": 1, "pid": 2304, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748254684900, "end": 1748254684901, "duration": 1, "pid": 2304, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748254684903, "end": 1748254684904, "duration": 1, "pid": 2304, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748254684904, "end": 1748254684905, "duration": 1, "pid": 2304, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748254684906, "end": 1748254684906, "duration": 0, "pid": 2304, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748254684908, "end": 1748254684922, "duration": 14, "pid": 2304, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748254684923, "end": 1748254684953, "duration": 30, "pid": 2304, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748254684954, "end": 1748254684962, "duration": 8, "pid": 2304, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748254684990, "end": 1748254687640, "duration": 2650, "pid": 2304, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748254686029, "end": 1748254686152, "duration": 123, "pid": 2304, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748254686059, "end": 1748254687522, "duration": 1463, "pid": 2304, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748254686203, "end": 1748254687670, "duration": 1467, "pid": 2304, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748254686420, "end": 1748254687636, "duration": 1216, "pid": 2304, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748254686645, "end": 1748254687562, "duration": 917, "pid": 2304, "index": 77}, {"name": "Load Service", "start": 1748254686646, "end": 1748254687055, "duration": 409, "pid": 2304, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748254686646, "end": 1748254687055, "duration": 409, "pid": 2304, "index": 79}, {"name": "Load Middleware", "start": 1748254687056, "end": 1748254687361, "duration": 305, "pid": 2304, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748254687056, "end": 1748254687344, "duration": 288, "pid": 2304, "index": 81}, {"name": "Load Controller", "start": 1748254687361, "end": 1748254687426, "duration": 65, "pid": 2304, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748254687361, "end": 1748254687426, "duration": 65, "pid": 2304, "index": 83}, {"name": "Load Router", "start": 1748254687426, "end": 1748254687435, "duration": 9, "pid": 2304, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748254687427, "end": 1748254687428, "duration": 1, "pid": 2304, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748254687429, "end": 1748254687522, "duration": 93, "pid": 2304, "index": 86}]