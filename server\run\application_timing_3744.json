[{"name": "Process Start", "start": 1748248872537, "end": 1748248877757, "duration": 5220, "pid": 3744, "index": 0}, {"name": "Application Start", "start": 1748248877762, "end": 1748248885390, "duration": 7628, "pid": 3744, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748248877889, "end": 1748248878035, "duration": 146, "pid": 3744, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748248878035, "end": 1748248879227, "duration": 1192, "pid": 3744, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748248878040, "end": 1748248878041, "duration": 1, "pid": 3744, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748248878049, "end": 1748248878059, "duration": 10, "pid": 3744, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748248878066, "end": 1748248878096, "duration": 30, "pid": 3744, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748248878099, "end": 1748248878102, "duration": 3, "pid": 3744, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748248878114, "end": 1748248878116, "duration": 2, "pid": 3744, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748248878135, "end": 1748248878182, "duration": 47, "pid": 3744, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748248878998, "end": 1748248879002, "duration": 4, "pid": 3744, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748248879010, "end": 1748248879013, "duration": 3, "pid": 3744, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748248879015, "end": 1748248879016, "duration": 1, "pid": 3744, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748248879017, "end": 1748248879019, "duration": 2, "pid": 3744, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748248879025, "end": 1748248879027, "duration": 2, "pid": 3744, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748248879030, "end": 1748248879032, "duration": 2, "pid": 3744, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748248879037, "end": 1748248879041, "duration": 4, "pid": 3744, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748248879043, "end": 1748248879045, "duration": 2, "pid": 3744, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748248879059, "end": 1748248879061, "duration": 2, "pid": 3744, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748248879066, "end": 1748248879069, "duration": 3, "pid": 3744, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748248879071, "end": 1748248879073, "duration": 2, "pid": 3744, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748248879079, "end": 1748248879081, "duration": 2, "pid": 3744, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748248879086, "end": 1748248879090, "duration": 4, "pid": 3744, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748248879094, "end": 1748248879097, "duration": 3, "pid": 3744, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748248879099, "end": 1748248879100, "duration": 1, "pid": 3744, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748248879114, "end": 1748248879115, "duration": 1, "pid": 3744, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748248879156, "end": 1748248879160, "duration": 4, "pid": 3744, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748248879173, "end": 1748248879193, "duration": 20, "pid": 3744, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748248879214, "end": 1748248879215, "duration": 1, "pid": 3744, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748248879225, "end": 1748248879227, "duration": 2, "pid": 3744, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748248879227, "end": 1748248879227, "duration": 0, "pid": 3744, "index": 30}, {"name": "Load extend/application.js", "start": 1748248879230, "end": 1748248880102, "duration": 872, "pid": 3744, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748248879244, "end": 1748248879248, "duration": 4, "pid": 3744, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748248879269, "end": 1748248879301, "duration": 32, "pid": 3744, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748248879303, "end": 1748248879340, "duration": 37, "pid": 3744, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748248879348, "end": 1748248879369, "duration": 21, "pid": 3744, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748248879374, "end": 1748248879382, "duration": 8, "pid": 3744, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748248879387, "end": 1748248879411, "duration": 24, "pid": 3744, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748248879414, "end": 1748248879925, "duration": 511, "pid": 3744, "index": 38}, {"name": "Load extend/request.js", "start": 1748248880102, "end": 1748248880380, "duration": 278, "pid": 3744, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748248880297, "end": 1748248880302, "duration": 5, "pid": 3744, "index": 40}, {"name": "Load extend/response.js", "start": 1748248880380, "end": 1748248880511, "duration": 131, "pid": 3744, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748248880466, "end": 1748248880484, "duration": 18, "pid": 3744, "index": 42}, {"name": "Load extend/context.js", "start": 1748248880511, "end": 1748248881196, "duration": 685, "pid": 3744, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748248880514, "end": 1748248880720, "duration": 206, "pid": 3744, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748248880733, "end": 1748248880794, "duration": 61, "pid": 3744, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748248880801, "end": 1748248880808, "duration": 7, "pid": 3744, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748248880813, "end": 1748248881113, "duration": 300, "pid": 3744, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748248881131, "end": 1748248881142, "duration": 11, "pid": 3744, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748248881147, "end": 1748248881149, "duration": 2, "pid": 3744, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748248881158, "end": 1748248881165, "duration": 7, "pid": 3744, "index": 50}, {"name": "Load extend/helper.js", "start": 1748248881196, "end": 1748248881449, "duration": 253, "pid": 3744, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748248881202, "end": 1748248881425, "duration": 223, "pid": 3744, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748248881434, "end": 1748248881435, "duration": 1, "pid": 3744, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748248881436, "end": 1748248881438, "duration": 2, "pid": 3744, "index": 54}, {"name": "Load app.js", "start": 1748248881450, "end": 1748248881920, "duration": 470, "pid": 3744, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748248881451, "end": 1748248881453, "duration": 2, "pid": 3744, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748248881456, "end": 1748248881474, "duration": 18, "pid": 3744, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748248881477, "end": 1748248881535, "duration": 58, "pid": 3744, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748248881537, "end": 1748248881690, "duration": 153, "pid": 3744, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748248881691, "end": 1748248881747, "duration": 56, "pid": 3744, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748248881748, "end": 1748248881750, "duration": 2, "pid": 3744, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748248881752, "end": 1748248881779, "duration": 27, "pid": 3744, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748248881781, "end": 1748248881782, "duration": 1, "pid": 3744, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748248881784, "end": 1748248881786, "duration": 2, "pid": 3744, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748248881787, "end": 1748248881792, "duration": 5, "pid": 3744, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748248881798, "end": 1748248881800, "duration": 2, "pid": 3744, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748248881801, "end": 1748248881802, "duration": 1, "pid": 3744, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748248881804, "end": 1748248881811, "duration": 7, "pid": 3744, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748248881812, "end": 1748248881828, "duration": 16, "pid": 3744, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748248881829, "end": 1748248881901, "duration": 72, "pid": 3744, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748248881902, "end": 1748248881914, "duration": 12, "pid": 3744, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748248881951, "end": 1748248885362, "duration": 3411, "pid": 3744, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748248883703, "end": 1748248883857, "duration": 154, "pid": 3744, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748248883751, "end": 1748248885202, "duration": 1451, "pid": 3744, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748248883924, "end": 1748248885389, "duration": 1465, "pid": 3744, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748248884110, "end": 1748248885365, "duration": 1255, "pid": 3744, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748248884312, "end": 1748248885276, "duration": 964, "pid": 3744, "index": 77}, {"name": "Load Service", "start": 1748248884313, "end": 1748248884565, "duration": 252, "pid": 3744, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748248884313, "end": 1748248884565, "duration": 252, "pid": 3744, "index": 79}, {"name": "Load Middleware", "start": 1748248884566, "end": 1748248884927, "duration": 361, "pid": 3744, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748248884566, "end": 1748248884899, "duration": 333, "pid": 3744, "index": 81}, {"name": "Load Controller", "start": 1748248884927, "end": 1748248885018, "duration": 91, "pid": 3744, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748248884927, "end": 1748248885018, "duration": 91, "pid": 3744, "index": 83}, {"name": "Load Router", "start": 1748248885018, "end": 1748248885034, "duration": 16, "pid": 3744, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748248885019, "end": 1748248885023, "duration": 4, "pid": 3744, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748248885025, "end": 1748248885202, "duration": 177, "pid": 3744, "index": 86}]