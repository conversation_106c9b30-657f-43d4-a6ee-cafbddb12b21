[{"name": "Process Start", "start": 1748252810154, "end": 1748252813691, "duration": 3537, "pid": 24836, "index": 0}, {"name": "Application Start", "start": 1748252813694, "end": 1748252819908, "duration": 6214, "pid": 24836, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748252813773, "end": 1748252813862, "duration": 89, "pid": 24836, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748252813863, "end": 1748252813945, "duration": 82, "pid": 24836, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748252813865, "end": 1748252813867, "duration": 2, "pid": 24836, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748252813870, "end": 1748252813871, "duration": 1, "pid": 24836, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748252813873, "end": 1748252813875, "duration": 2, "pid": 24836, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748252813876, "end": 1748252813878, "duration": 2, "pid": 24836, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748252813880, "end": 1748252813881, "duration": 1, "pid": 24836, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748252813882, "end": 1748252813882, "duration": 0, "pid": 24836, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748252813884, "end": 1748252813885, "duration": 1, "pid": 24836, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748252813887, "end": 1748252813889, "duration": 2, "pid": 24836, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748252813890, "end": 1748252813890, "duration": 0, "pid": 24836, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748252813892, "end": 1748252813893, "duration": 1, "pid": 24836, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748252813895, "end": 1748252813896, "duration": 1, "pid": 24836, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748252813898, "end": 1748252813899, "duration": 1, "pid": 24836, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748252813900, "end": 1748252813902, "duration": 2, "pid": 24836, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748252813903, "end": 1748252813904, "duration": 1, "pid": 24836, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748252813906, "end": 1748252813907, "duration": 1, "pid": 24836, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748252813908, "end": 1748252813909, "duration": 1, "pid": 24836, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748252813912, "end": 1748252813914, "duration": 2, "pid": 24836, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748252813915, "end": 1748252813917, "duration": 2, "pid": 24836, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748252813919, "end": 1748252813920, "duration": 1, "pid": 24836, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748252813922, "end": 1748252813923, "duration": 1, "pid": 24836, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748252813923, "end": 1748252813924, "duration": 1, "pid": 24836, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748252813928, "end": 1748252813928, "duration": 0, "pid": 24836, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748252813931, "end": 1748252813931, "duration": 0, "pid": 24836, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748252813933, "end": 1748252813934, "duration": 1, "pid": 24836, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748252813938, "end": 1748252813939, "duration": 1, "pid": 24836, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748252813944, "end": 1748252813945, "duration": 1, "pid": 24836, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748252813945, "end": 1748252813945, "duration": 0, "pid": 24836, "index": 30}, {"name": "Load extend/application.js", "start": 1748252813947, "end": 1748252814152, "duration": 205, "pid": 24836, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748252813949, "end": 1748252813951, "duration": 2, "pid": 24836, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748252813953, "end": 1748252813958, "duration": 5, "pid": 24836, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748252813959, "end": 1748252813972, "duration": 13, "pid": 24836, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748252813974, "end": 1748252813988, "duration": 14, "pid": 24836, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748252813989, "end": 1748252813992, "duration": 3, "pid": 24836, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748252813995, "end": 1748252813999, "duration": 4, "pid": 24836, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748252814001, "end": 1748252814129, "duration": 128, "pid": 24836, "index": 38}, {"name": "Load extend/request.js", "start": 1748252814152, "end": 1748252814191, "duration": 39, "pid": 24836, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748252814171, "end": 1748252814175, "duration": 4, "pid": 24836, "index": 40}, {"name": "Load extend/response.js", "start": 1748252814191, "end": 1748252814230, "duration": 39, "pid": 24836, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748252814210, "end": 1748252814219, "duration": 9, "pid": 24836, "index": 42}, {"name": "Load extend/context.js", "start": 1748252814230, "end": 1748252814359, "duration": 129, "pid": 24836, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748252814232, "end": 1748252814272, "duration": 40, "pid": 24836, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748252814272, "end": 1748252814276, "duration": 4, "pid": 24836, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748252814278, "end": 1748252814280, "duration": 2, "pid": 24836, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748252814282, "end": 1748252814338, "duration": 56, "pid": 24836, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748252814340, "end": 1748252814342, "duration": 2, "pid": 24836, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748252814345, "end": 1748252814346, "duration": 1, "pid": 24836, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748252814348, "end": 1748252814351, "duration": 3, "pid": 24836, "index": 50}, {"name": "Load extend/helper.js", "start": 1748252814359, "end": 1748252814419, "duration": 60, "pid": 24836, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748252814360, "end": 1748252814402, "duration": 42, "pid": 24836, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748252814408, "end": 1748252814408, "duration": 0, "pid": 24836, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748252814409, "end": 1748252814410, "duration": 1, "pid": 24836, "index": 54}, {"name": "Load app.js", "start": 1748252814419, "end": 1748252814556, "duration": 137, "pid": 24836, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748252814420, "end": 1748252814421, "duration": 1, "pid": 24836, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748252814421, "end": 1748252814425, "duration": 4, "pid": 24836, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748252814426, "end": 1748252814452, "duration": 26, "pid": 24836, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748252814453, "end": 1748252814476, "duration": 23, "pid": 24836, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748252814476, "end": 1748252814496, "duration": 20, "pid": 24836, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748252814497, "end": 1748252814498, "duration": 1, "pid": 24836, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748252814499, "end": 1748252814502, "duration": 3, "pid": 24836, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748252814503, "end": 1748252814503, "duration": 0, "pid": 24836, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748252814504, "end": 1748252814505, "duration": 1, "pid": 24836, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748252814505, "end": 1748252814506, "duration": 1, "pid": 24836, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748252814507, "end": 1748252814507, "duration": 0, "pid": 24836, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748252814508, "end": 1748252814508, "duration": 0, "pid": 24836, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748252814509, "end": 1748252814510, "duration": 1, "pid": 24836, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748252814511, "end": 1748252814517, "duration": 6, "pid": 24836, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748252814518, "end": 1748252814546, "duration": 28, "pid": 24836, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748252814547, "end": 1748252814555, "duration": 8, "pid": 24836, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748252814574, "end": 1748252819865, "duration": 5291, "pid": 24836, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748252816206, "end": 1748252816857, "duration": 651, "pid": 24836, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748252816404, "end": 1748252819422, "duration": 3018, "pid": 24836, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748252816898, "end": 1748252819907, "duration": 3009, "pid": 24836, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748252817383, "end": 1748252819858, "duration": 2475, "pid": 24836, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748252817791, "end": 1748252819730, "duration": 1939, "pid": 24836, "index": 77}, {"name": "Load Service", "start": 1748252817791, "end": 1748252818195, "duration": 404, "pid": 24836, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748252817792, "end": 1748252818195, "duration": 403, "pid": 24836, "index": 79}, {"name": "Load Middleware", "start": 1748252818196, "end": 1748252818883, "duration": 687, "pid": 24836, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748252818197, "end": 1748252818860, "duration": 663, "pid": 24836, "index": 81}, {"name": "Load Controller", "start": 1748252818883, "end": 1748252819086, "duration": 203, "pid": 24836, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748252818883, "end": 1748252819086, "duration": 203, "pid": 24836, "index": 83}, {"name": "Load Router", "start": 1748252819086, "end": 1748252819106, "duration": 20, "pid": 24836, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748252819087, "end": 1748252819089, "duration": 2, "pid": 24836, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748252819093, "end": 1748252819422, "duration": 329, "pid": 24836, "index": 86}]