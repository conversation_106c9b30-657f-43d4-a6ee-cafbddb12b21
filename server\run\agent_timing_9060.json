[{"name": "Process Start", "start": 1748252508777, "end": 1748252512800, "duration": 4023, "pid": 9060, "index": 0}, {"name": "Application Start", "start": 1748252512802, "end": 1748252514362, "duration": 1560, "pid": 9060, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748252512821, "end": 1748252512849, "duration": 28, "pid": 9060, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748252512850, "end": 1748252512894, "duration": 44, "pid": 9060, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748252512850, "end": 1748252512851, "duration": 1, "pid": 9060, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748252512854, "end": 1748252512855, "duration": 1, "pid": 9060, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748252512857, "end": 1748252512857, "duration": 0, "pid": 9060, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748252512858, "end": 1748252512859, "duration": 1, "pid": 9060, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748252512861, "end": 1748252512862, "duration": 1, "pid": 9060, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748252512863, "end": 1748252512864, "duration": 1, "pid": 9060, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748252512864, "end": 1748252512865, "duration": 1, "pid": 9060, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748252512866, "end": 1748252512867, "duration": 1, "pid": 9060, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748252512868, "end": 1748252512868, "duration": 0, "pid": 9060, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748252512869, "end": 1748252512869, "duration": 0, "pid": 9060, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748252512870, "end": 1748252512870, "duration": 0, "pid": 9060, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748252512871, "end": 1748252512871, "duration": 0, "pid": 9060, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748252512872, "end": 1748252512873, "duration": 1, "pid": 9060, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748252512873, "end": 1748252512874, "duration": 1, "pid": 9060, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748252512875, "end": 1748252512875, "duration": 0, "pid": 9060, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748252512876, "end": 1748252512876, "duration": 0, "pid": 9060, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748252512877, "end": 1748252512878, "duration": 1, "pid": 9060, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748252512878, "end": 1748252512879, "duration": 1, "pid": 9060, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748252512879, "end": 1748252512879, "duration": 0, "pid": 9060, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748252512880, "end": 1748252512880, "duration": 0, "pid": 9060, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748252512881, "end": 1748252512881, "duration": 0, "pid": 9060, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748252512883, "end": 1748252512883, "duration": 0, "pid": 9060, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748252512885, "end": 1748252512885, "duration": 0, "pid": 9060, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748252512887, "end": 1748252512887, "duration": 0, "pid": 9060, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748252512889, "end": 1748252512890, "duration": 1, "pid": 9060, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748252512894, "end": 1748252512894, "duration": 0, "pid": 9060, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748252512894, "end": 1748252512894, "duration": 0, "pid": 9060, "index": 30}, {"name": "Load extend/agent.js", "start": 1748252512896, "end": 1748252513020, "duration": 124, "pid": 9060, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/agent.js", "start": 1748252512897, "end": 1748252512900, "duration": 3, "pid": 9060, "index": 32}, {"name": "Require(28) node_modules/egg-schedule/app/extend/agent.js", "start": 1748252512902, "end": 1748252513005, "duration": 103, "pid": 9060, "index": 33}, {"name": "Require(29) node_modules/egg-logrotator/app/extend/agent.js", "start": 1748252513006, "end": 1748252513009, "duration": 3, "pid": 9060, "index": 34}, {"name": "Load extend/context.js", "start": 1748252513020, "end": 1748252513111, "duration": 91, "pid": 9060, "index": 35}, {"name": "Require(30) node_modules/egg-security/app/extend/context.js", "start": 1748252513021, "end": 1748252513045, "duration": 24, "pid": 9060, "index": 36}, {"name": "Require(31) node_modules/egg-jsonp/app/extend/context.js", "start": 1748252513046, "end": 1748252513052, "duration": 6, "pid": 9060, "index": 37}, {"name": "Require(32) node_modules/egg-i18n/app/extend/context.js", "start": 1748252513053, "end": 1748252513054, "duration": 1, "pid": 9060, "index": 38}, {"name": "Require(33) node_modules/egg-multipart/app/extend/context.js", "start": 1748252513056, "end": 1748252513092, "duration": 36, "pid": 9060, "index": 39}, {"name": "Require(34) node_modules/egg-view/app/extend/context.js", "start": 1748252513093, "end": 1748252513096, "duration": 3, "pid": 9060, "index": 40}, {"name": "Require(35) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748252513098, "end": 1748252513098, "duration": 0, "pid": 9060, "index": 41}, {"name": "Require(36) node_modules/egg/app/extend/context.js", "start": 1748252513100, "end": 1748252513103, "duration": 3, "pid": 9060, "index": 42}, {"name": "Load agent.js", "start": 1748252513111, "end": 1748252513196, "duration": 85, "pid": 9060, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1748252513112, "end": 1748252513113, "duration": 1, "pid": 9060, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1748252513114, "end": 1748252513115, "duration": 1, "pid": 9060, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1748252513116, "end": 1748252513137, "duration": 21, "pid": 9060, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1748252513138, "end": 1748252513141, "duration": 3, "pid": 9060, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1748252513142, "end": 1748252513160, "duration": 18, "pid": 9060, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1748252513161, "end": 1748252513161, "duration": 0, "pid": 9060, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1748252513162, "end": 1748252513162, "duration": 0, "pid": 9060, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1748252513164, "end": 1748252513184, "duration": 20, "pid": 9060, "index": 51}, {"name": "Require(45) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1748252513185, "end": 1748252513194, "duration": 9, "pid": 9060, "index": 52}, {"name": "Require(46) node_modules/egg/agent.js", "start": 1748252513195, "end": 1748252513195, "duration": 0, "pid": 9060, "index": 53}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748252513203, "end": 1748252514170, "duration": 967, "pid": 9060, "index": 54}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1748252513204, "end": 1748252514137, "duration": 933, "pid": 9060, "index": 55}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1748252513204, "end": 1748252514362, "duration": 1158, "pid": 9060, "index": 56}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748252513929, "end": 1748252514016, "duration": 87, "pid": 9060, "index": 57}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748252513952, "end": 1748252514167, "duration": 215, "pid": 9060, "index": 58}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748252514042, "end": 1748252514359, "duration": 317, "pid": 9060, "index": 59}]