[{"name": "Process Start", "start": 1748253035449, "end": 1748253039029, "duration": 3580, "pid": 31064, "index": 0}, {"name": "Application Start", "start": 1748253039032, "end": 1748253048060, "duration": 9028, "pid": 31064, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748253039100, "end": 1748253039304, "duration": 204, "pid": 31064, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748253039304, "end": 1748253039466, "duration": 162, "pid": 31064, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748253039309, "end": 1748253039310, "duration": 1, "pid": 31064, "index": 4}, {"name": "Require(1) config/config.prod.js", "start": 1748253039314, "end": 1748253039314, "duration": 0, "pid": 31064, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748253039319, "end": 1748253039320, "duration": 1, "pid": 31064, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748253039321, "end": 1748253039322, "duration": 1, "pid": 31064, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748253039324, "end": 1748253039332, "duration": 8, "pid": 31064, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748253039333, "end": 1748253039338, "duration": 5, "pid": 31064, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748253039343, "end": 1748253039344, "duration": 1, "pid": 31064, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748253039345, "end": 1748253039346, "duration": 1, "pid": 31064, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748253039348, "end": 1748253039349, "duration": 1, "pid": 31064, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748253039350, "end": 1748253039353, "duration": 3, "pid": 31064, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1748253039356, "end": 1748253039357, "duration": 1, "pid": 31064, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1748253039358, "end": 1748253039359, "duration": 1, "pid": 31064, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1748253039360, "end": 1748253039361, "duration": 1, "pid": 31064, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1748253039362, "end": 1748253039362, "duration": 0, "pid": 31064, "index": 17}, {"name": "Require(14) node_modules/egg-jwt/config/config.default.js", "start": 1748253039364, "end": 1748253039364, "duration": 0, "pid": 31064, "index": 18}, {"name": "Require(15) node_modules/egg-cors/config/config.default.js", "start": 1748253039365, "end": 1748253039366, "duration": 1, "pid": 31064, "index": 19}, {"name": "Require(16) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748253039367, "end": 1748253039367, "duration": 0, "pid": 31064, "index": 20}, {"name": "Require(17) node_modules/egg-mysql/config/config.default.js", "start": 1748253039368, "end": 1748253039369, "duration": 1, "pid": 31064, "index": 21}, {"name": "Require(18) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748253039370, "end": 1748253039371, "duration": 1, "pid": 31064, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748253039372, "end": 1748253039372, "duration": 0, "pid": 31064, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748253039375, "end": 1748253039376, "duration": 1, "pid": 31064, "index": 24}, {"name": "Require(21) node_modules/egg-static/config/config.prod.js", "start": 1748253039443, "end": 1748253039445, "duration": 2, "pid": 31064, "index": 25}, {"name": "Require(22) config/config.prod.js", "start": 1748253039466, "end": 1748253039466, "duration": 0, "pid": 31064, "index": 26}, {"name": "Load extend/application.js", "start": 1748253039470, "end": 1748253040205, "duration": 735, "pid": 31064, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1748253039472, "end": 1748253039475, "duration": 3, "pid": 31064, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1748253039478, "end": 1748253039481, "duration": 3, "pid": 31064, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1748253039487, "end": 1748253039517, "duration": 30, "pid": 31064, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1748253039519, "end": 1748253039533, "duration": 14, "pid": 31064, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1748253039536, "end": 1748253039541, "duration": 5, "pid": 31064, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1748253039543, "end": 1748253039557, "duration": 14, "pid": 31064, "index": 33}, {"name": "Require(29) node_modules/egg-jwt/app/extend/application.js", "start": 1748253039560, "end": 1748253039915, "duration": 355, "pid": 31064, "index": 34}, {"name": "Load extend/request.js", "start": 1748253040205, "end": 1748253040311, "duration": 106, "pid": 31064, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1748253040242, "end": 1748253040248, "duration": 6, "pid": 31064, "index": 36}, {"name": "Load extend/response.js", "start": 1748253040311, "end": 1748253040340, "duration": 29, "pid": 31064, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1748253040321, "end": 1748253040326, "duration": 5, "pid": 31064, "index": 38}, {"name": "Load extend/context.js", "start": 1748253040340, "end": 1748253040651, "duration": 311, "pid": 31064, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1748253040342, "end": 1748253040435, "duration": 93, "pid": 31064, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1748253040435, "end": 1748253040440, "duration": 5, "pid": 31064, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1748253040508, "end": 1748253040509, "duration": 1, "pid": 31064, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1748253040511, "end": 1748253040595, "duration": 84, "pid": 31064, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1748253040599, "end": 1748253040602, "duration": 3, "pid": 31064, "index": 44}, {"name": "Require(37) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748253040612, "end": 1748253040612, "duration": 0, "pid": 31064, "index": 45}, {"name": "Require(38) node_modules/egg/app/extend/context.js", "start": 1748253040614, "end": 1748253040622, "duration": 8, "pid": 31064, "index": 46}, {"name": "Load extend/helper.js", "start": 1748253040651, "end": 1748253040912, "duration": 261, "pid": 31064, "index": 47}, {"name": "Require(39) node_modules/egg-security/app/extend/helper.js", "start": 1748253040652, "end": 1748253040857, "duration": 205, "pid": 31064, "index": 48}, {"name": "Require(40) node_modules/egg/app/extend/helper.js", "start": 1748253040867, "end": 1748253040869, "duration": 2, "pid": 31064, "index": 49}, {"name": "Require(41) app/extend/helper.js", "start": 1748253040892, "end": 1748253040895, "duration": 3, "pid": 31064, "index": 50}, {"name": "Load app.js", "start": 1748253040912, "end": 1748253041308, "duration": 396, "pid": 31064, "index": 51}, {"name": "Require(42) node_modules/egg-session/app.js", "start": 1748253040913, "end": 1748253040919, "duration": 6, "pid": 31064, "index": 52}, {"name": "Require(43) node_modules/egg-security/app.js", "start": 1748253040924, "end": 1748253040945, "duration": 21, "pid": 31064, "index": 53}, {"name": "Require(44) node_modules/egg-onerror/app.js", "start": 1748253040947, "end": 1748253041132, "duration": 185, "pid": 31064, "index": 54}, {"name": "Require(45) node_modules/egg-i18n/app.js", "start": 1748253041133, "end": 1748253041162, "duration": 29, "pid": 31064, "index": 55}, {"name": "Require(46) node_modules/egg-watcher/app.js", "start": 1748253041163, "end": 1748253041208, "duration": 45, "pid": 31064, "index": 56}, {"name": "Require(47) node_modules/egg-schedule/app.js", "start": 1748253041209, "end": 1748253041210, "duration": 1, "pid": 31064, "index": 57}, {"name": "Require(48) node_modules/egg-multipart/app.js", "start": 1748253041211, "end": 1748253041218, "duration": 7, "pid": 31064, "index": 58}, {"name": "Require(49) node_modules/egg-logrotator/app.js", "start": 1748253041219, "end": 1748253041219, "duration": 0, "pid": 31064, "index": 59}, {"name": "Require(50) node_modules/egg-static/app.js", "start": 1748253041220, "end": 1748253041221, "duration": 1, "pid": 31064, "index": 60}, {"name": "Require(51) node_modules/egg-sequelize/app.js", "start": 1748253041224, "end": 1748253041225, "duration": 1, "pid": 31064, "index": 61}, {"name": "Require(52) node_modules/egg-jwt/app.js", "start": 1748253041226, "end": 1748253041226, "duration": 0, "pid": 31064, "index": 62}, {"name": "Require(53) node_modules/egg-cors/app.js", "start": 1748253041229, "end": 1748253041230, "duration": 1, "pid": 31064, "index": 63}, {"name": "Require(54) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748253041231, "end": 1748253041239, "duration": 8, "pid": 31064, "index": 64}, {"name": "Require(55) node_modules/egg-mysql/app.js", "start": 1748253041240, "end": 1748253041290, "duration": 50, "pid": 31064, "index": 65}, {"name": "Require(56) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748253041291, "end": 1748253041301, "duration": 10, "pid": 31064, "index": 66}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748253041350, "end": 1748253048024, "duration": 6674, "pid": 31064, "index": 67}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748253043738, "end": 1748253044130, "duration": 392, "pid": 31064, "index": 68}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748253043825, "end": 1748253047654, "duration": 3829, "pid": 31064, "index": 69}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748253044209, "end": 1748253048056, "duration": 3847, "pid": 31064, "index": 70}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748253044696, "end": 1748253048014, "duration": 3318, "pid": 31064, "index": 71}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748253045199, "end": 1748253047895, "duration": 2696, "pid": 31064, "index": 72}, {"name": "Load Service", "start": 1748253045199, "end": 1748253045790, "duration": 591, "pid": 31064, "index": 73}, {"name": "Load \"service\" to Context", "start": 1748253045200, "end": 1748253045790, "duration": 590, "pid": 31064, "index": 74}, {"name": "Load Middleware", "start": 1748253045791, "end": 1748253046446, "duration": 655, "pid": 31064, "index": 75}, {"name": "Load \"middlewares\" to Application", "start": 1748253045792, "end": 1748253046378, "duration": 586, "pid": 31064, "index": 76}, {"name": "Load Controller", "start": 1748253046446, "end": 1748253047184, "duration": 738, "pid": 31064, "index": 77}, {"name": "Load \"controller\" to Application", "start": 1748253046446, "end": 1748253047184, "duration": 738, "pid": 31064, "index": 78}, {"name": "Load Router", "start": 1748253047184, "end": 1748253047250, "duration": 66, "pid": 31064, "index": 79}, {"name": "Require(57) app/router.js", "start": 1748253047189, "end": 1748253047191, "duration": 2, "pid": 31064, "index": 80}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748253047197, "end": 1748253047653, "duration": 456, "pid": 31064, "index": 81}]