[{"name": "Process Start", "start": 1748253132415, "end": 1748253134879, "duration": 2464, "pid": 4192, "index": 0}, {"name": "Application Start", "start": 1748253134881, "end": 1748253139220, "duration": 4339, "pid": 4192, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748253134926, "end": 1748253135007, "duration": 81, "pid": 4192, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748253135007, "end": 1748253135085, "duration": 78, "pid": 4192, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748253135009, "end": 1748253135010, "duration": 1, "pid": 4192, "index": 4}, {"name": "Require(1) config/config.prod.js", "start": 1748253135013, "end": 1748253135014, "duration": 1, "pid": 4192, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748253135015, "end": 1748253135016, "duration": 1, "pid": 4192, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748253135017, "end": 1748253135018, "duration": 1, "pid": 4192, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748253135022, "end": 1748253135023, "duration": 1, "pid": 4192, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748253135025, "end": 1748253135026, "duration": 1, "pid": 4192, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748253135027, "end": 1748253135029, "duration": 2, "pid": 4192, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748253135031, "end": 1748253135032, "duration": 1, "pid": 4192, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748253135034, "end": 1748253135034, "duration": 0, "pid": 4192, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748253135037, "end": 1748253135038, "duration": 1, "pid": 4192, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1748253135041, "end": 1748253135042, "duration": 1, "pid": 4192, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1748253135044, "end": 1748253135044, "duration": 0, "pid": 4192, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1748253135046, "end": 1748253135046, "duration": 0, "pid": 4192, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1748253135048, "end": 1748253135048, "duration": 0, "pid": 4192, "index": 17}, {"name": "Require(14) node_modules/egg-jwt/config/config.default.js", "start": 1748253135050, "end": 1748253135050, "duration": 0, "pid": 4192, "index": 18}, {"name": "Require(15) node_modules/egg-cors/config/config.default.js", "start": 1748253135051, "end": 1748253135052, "duration": 1, "pid": 4192, "index": 19}, {"name": "Require(16) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748253135061, "end": 1748253135062, "duration": 1, "pid": 4192, "index": 20}, {"name": "Require(17) node_modules/egg-mysql/config/config.default.js", "start": 1748253135063, "end": 1748253135063, "duration": 0, "pid": 4192, "index": 21}, {"name": "Require(18) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748253135064, "end": 1748253135066, "duration": 2, "pid": 4192, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748253135067, "end": 1748253135068, "duration": 1, "pid": 4192, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748253135070, "end": 1748253135070, "duration": 0, "pid": 4192, "index": 24}, {"name": "Require(21) node_modules/egg-static/config/config.prod.js", "start": 1748253135079, "end": 1748253135080, "duration": 1, "pid": 4192, "index": 25}, {"name": "Require(22) config/config.prod.js", "start": 1748253135085, "end": 1748253135085, "duration": 0, "pid": 4192, "index": 26}, {"name": "Load extend/application.js", "start": 1748253135091, "end": 1748253135345, "duration": 254, "pid": 4192, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1748253135092, "end": 1748253135093, "duration": 1, "pid": 4192, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1748253135094, "end": 1748253135098, "duration": 4, "pid": 4192, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1748253135100, "end": 1748253135127, "duration": 27, "pid": 4192, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1748253135130, "end": 1748253135157, "duration": 27, "pid": 4192, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1748253135160, "end": 1748253135166, "duration": 6, "pid": 4192, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1748253135175, "end": 1748253135179, "duration": 4, "pid": 4192, "index": 33}, {"name": "Require(29) node_modules/egg-jwt/app/extend/application.js", "start": 1748253135192, "end": 1748253135330, "duration": 138, "pid": 4192, "index": 34}, {"name": "Load extend/request.js", "start": 1748253135345, "end": 1748253135380, "duration": 35, "pid": 4192, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1748253135357, "end": 1748253135360, "duration": 3, "pid": 4192, "index": 36}, {"name": "Load extend/response.js", "start": 1748253135380, "end": 1748253135428, "duration": 48, "pid": 4192, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1748253135401, "end": 1748253135409, "duration": 8, "pid": 4192, "index": 38}, {"name": "Load extend/context.js", "start": 1748253135428, "end": 1748253135555, "duration": 127, "pid": 4192, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1748253135429, "end": 1748253135459, "duration": 30, "pid": 4192, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1748253135460, "end": 1748253135464, "duration": 4, "pid": 4192, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1748253135466, "end": 1748253135467, "duration": 1, "pid": 4192, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1748253135470, "end": 1748253135528, "duration": 58, "pid": 4192, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1748253135529, "end": 1748253135531, "duration": 2, "pid": 4192, "index": 44}, {"name": "Require(37) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748253135533, "end": 1748253135534, "duration": 1, "pid": 4192, "index": 45}, {"name": "Require(38) node_modules/egg/app/extend/context.js", "start": 1748253135536, "end": 1748253135540, "duration": 4, "pid": 4192, "index": 46}, {"name": "Load extend/helper.js", "start": 1748253135555, "end": 1748253135663, "duration": 108, "pid": 4192, "index": 47}, {"name": "Require(39) node_modules/egg-security/app/extend/helper.js", "start": 1748253135557, "end": 1748253135629, "duration": 72, "pid": 4192, "index": 48}, {"name": "Require(40) node_modules/egg/app/extend/helper.js", "start": 1748253135646, "end": 1748253135647, "duration": 1, "pid": 4192, "index": 49}, {"name": "Require(41) app/extend/helper.js", "start": 1748253135647, "end": 1748253135648, "duration": 1, "pid": 4192, "index": 50}, {"name": "Load app.js", "start": 1748253135664, "end": 1748253135936, "duration": 272, "pid": 4192, "index": 51}, {"name": "Require(42) node_modules/egg-session/app.js", "start": 1748253135664, "end": 1748253135665, "duration": 1, "pid": 4192, "index": 52}, {"name": "Require(43) node_modules/egg-security/app.js", "start": 1748253135666, "end": 1748253135670, "duration": 4, "pid": 4192, "index": 53}, {"name": "Require(44) node_modules/egg-onerror/app.js", "start": 1748253135672, "end": 1748253135698, "duration": 26, "pid": 4192, "index": 54}, {"name": "Require(45) node_modules/egg-i18n/app.js", "start": 1748253135726, "end": 1748253135752, "duration": 26, "pid": 4192, "index": 55}, {"name": "Require(46) node_modules/egg-watcher/app.js", "start": 1748253135755, "end": 1748253135817, "duration": 62, "pid": 4192, "index": 56}, {"name": "Require(47) node_modules/egg-schedule/app.js", "start": 1748253135818, "end": 1748253135821, "duration": 3, "pid": 4192, "index": 57}, {"name": "Require(48) node_modules/egg-multipart/app.js", "start": 1748253135823, "end": 1748253135828, "duration": 5, "pid": 4192, "index": 58}, {"name": "Require(49) node_modules/egg-logrotator/app.js", "start": 1748253135828, "end": 1748253135829, "duration": 1, "pid": 4192, "index": 59}, {"name": "Require(50) node_modules/egg-static/app.js", "start": 1748253135830, "end": 1748253135831, "duration": 1, "pid": 4192, "index": 60}, {"name": "Require(51) node_modules/egg-sequelize/app.js", "start": 1748253135834, "end": 1748253135835, "duration": 1, "pid": 4192, "index": 61}, {"name": "Require(52) node_modules/egg-jwt/app.js", "start": 1748253135838, "end": 1748253135838, "duration": 0, "pid": 4192, "index": 62}, {"name": "Require(53) node_modules/egg-cors/app.js", "start": 1748253135839, "end": 1748253135867, "duration": 28, "pid": 4192, "index": 63}, {"name": "Require(54) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748253135870, "end": 1748253135874, "duration": 4, "pid": 4192, "index": 64}, {"name": "Require(55) node_modules/egg-mysql/app.js", "start": 1748253135874, "end": 1748253135906, "duration": 32, "pid": 4192, "index": 65}, {"name": "Require(56) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748253135908, "end": 1748253135933, "duration": 25, "pid": 4192, "index": 66}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748253135959, "end": 1748253139196, "duration": 3237, "pid": 4192, "index": 67}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748253137375, "end": 1748253137637, "duration": 262, "pid": 4192, "index": 68}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748253137453, "end": 1748253139041, "duration": 1588, "pid": 4192, "index": 69}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748253137749, "end": 1748253139219, "duration": 1470, "pid": 4192, "index": 70}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748253138008, "end": 1748253139189, "duration": 1181, "pid": 4192, "index": 71}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748253138241, "end": 1748253139086, "duration": 845, "pid": 4192, "index": 72}, {"name": "Load Service", "start": 1748253138242, "end": 1748253138546, "duration": 304, "pid": 4192, "index": 73}, {"name": "Load \"service\" to Context", "start": 1748253138242, "end": 1748253138546, "duration": 304, "pid": 4192, "index": 74}, {"name": "Load Middleware", "start": 1748253138546, "end": 1748253138853, "duration": 307, "pid": 4192, "index": 75}, {"name": "Load \"middlewares\" to Application", "start": 1748253138547, "end": 1748253138818, "duration": 271, "pid": 4192, "index": 76}, {"name": "Load Controller", "start": 1748253138853, "end": 1748253138941, "duration": 88, "pid": 4192, "index": 77}, {"name": "Load \"controller\" to Application", "start": 1748253138854, "end": 1748253138941, "duration": 87, "pid": 4192, "index": 78}, {"name": "Load Router", "start": 1748253138941, "end": 1748253138952, "duration": 11, "pid": 4192, "index": 79}, {"name": "Require(57) app/router.js", "start": 1748253138941, "end": 1748253138942, "duration": 1, "pid": 4192, "index": 80}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748253138944, "end": 1748253139040, "duration": 96, "pid": 4192, "index": 81}]