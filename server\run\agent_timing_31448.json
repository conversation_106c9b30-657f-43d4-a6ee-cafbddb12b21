[{"name": "Process Start", "start": 1748248468264, "end": 1748248474652, "duration": 6388, "pid": 31448, "index": 0}, {"name": "Application Start", "start": 1748248474655, "end": 1748248477008, "duration": 2353, "pid": 31448, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748248474687, "end": 1748248474744, "duration": 57, "pid": 31448, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748248474745, "end": 1748248474805, "duration": 60, "pid": 31448, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748248474746, "end": 1748248474747, "duration": 1, "pid": 31448, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748248474752, "end": 1748248474752, "duration": 0, "pid": 31448, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748248474753, "end": 1748248474754, "duration": 1, "pid": 31448, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748248474756, "end": 1748248474756, "duration": 0, "pid": 31448, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748248474758, "end": 1748248474759, "duration": 1, "pid": 31448, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748248474761, "end": 1748248474761, "duration": 0, "pid": 31448, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748248474762, "end": 1748248474763, "duration": 1, "pid": 31448, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748248474764, "end": 1748248474765, "duration": 1, "pid": 31448, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748248474766, "end": 1748248474766, "duration": 0, "pid": 31448, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748248474767, "end": 1748248474768, "duration": 1, "pid": 31448, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748248474769, "end": 1748248474770, "duration": 1, "pid": 31448, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748248474770, "end": 1748248474771, "duration": 1, "pid": 31448, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748248474772, "end": 1748248474772, "duration": 0, "pid": 31448, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748248474773, "end": 1748248474773, "duration": 0, "pid": 31448, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748248474774, "end": 1748248474775, "duration": 1, "pid": 31448, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748248474776, "end": 1748248474777, "duration": 1, "pid": 31448, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748248474777, "end": 1748248474778, "duration": 1, "pid": 31448, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748248474779, "end": 1748248474779, "duration": 0, "pid": 31448, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748248474781, "end": 1748248474782, "duration": 1, "pid": 31448, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748248474783, "end": 1748248474784, "duration": 1, "pid": 31448, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748248474784, "end": 1748248474785, "duration": 1, "pid": 31448, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748248474787, "end": 1748248474787, "duration": 0, "pid": 31448, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748248474788, "end": 1748248474789, "duration": 1, "pid": 31448, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748248474792, "end": 1748248474794, "duration": 2, "pid": 31448, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748248474798, "end": 1748248474799, "duration": 1, "pid": 31448, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748248474803, "end": 1748248474804, "duration": 1, "pid": 31448, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748248474804, "end": 1748248474804, "duration": 0, "pid": 31448, "index": 30}, {"name": "Load extend/agent.js", "start": 1748248474812, "end": 1748248474990, "duration": 178, "pid": 31448, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/agent.js", "start": 1748248474817, "end": 1748248474820, "duration": 3, "pid": 31448, "index": 32}, {"name": "Require(28) node_modules/egg-schedule/app/extend/agent.js", "start": 1748248474824, "end": 1748248474941, "duration": 117, "pid": 31448, "index": 33}, {"name": "Require(29) node_modules/egg-logrotator/app/extend/agent.js", "start": 1748248474944, "end": 1748248474948, "duration": 4, "pid": 31448, "index": 34}, {"name": "Load extend/context.js", "start": 1748248474990, "end": 1748248475106, "duration": 116, "pid": 31448, "index": 35}, {"name": "Require(30) node_modules/egg-security/app/extend/context.js", "start": 1748248474991, "end": 1748248475027, "duration": 36, "pid": 31448, "index": 36}, {"name": "Require(31) node_modules/egg-jsonp/app/extend/context.js", "start": 1748248475029, "end": 1748248475034, "duration": 5, "pid": 31448, "index": 37}, {"name": "Require(32) node_modules/egg-i18n/app/extend/context.js", "start": 1748248475035, "end": 1748248475036, "duration": 1, "pid": 31448, "index": 38}, {"name": "Require(33) node_modules/egg-multipart/app/extend/context.js", "start": 1748248475037, "end": 1748248475081, "duration": 44, "pid": 31448, "index": 39}, {"name": "Require(34) node_modules/egg-view/app/extend/context.js", "start": 1748248475084, "end": 1748248475088, "duration": 4, "pid": 31448, "index": 40}, {"name": "Require(35) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748248475090, "end": 1748248475091, "duration": 1, "pid": 31448, "index": 41}, {"name": "Require(36) node_modules/egg/app/extend/context.js", "start": 1748248475092, "end": 1748248475096, "duration": 4, "pid": 31448, "index": 42}, {"name": "Load agent.js", "start": 1748248475106, "end": 1748248475220, "duration": 114, "pid": 31448, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1748248475107, "end": 1748248475108, "duration": 1, "pid": 31448, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1748248475109, "end": 1748248475110, "duration": 1, "pid": 31448, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1748248475111, "end": 1748248475130, "duration": 19, "pid": 31448, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1748248475132, "end": 1748248475135, "duration": 3, "pid": 31448, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1748248475136, "end": 1748248475165, "duration": 29, "pid": 31448, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1748248475166, "end": 1748248475166, "duration": 0, "pid": 31448, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1748248475168, "end": 1748248475169, "duration": 1, "pid": 31448, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1748248475171, "end": 1748248475204, "duration": 33, "pid": 31448, "index": 51}, {"name": "Require(45) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1748248475205, "end": 1748248475217, "duration": 12, "pid": 31448, "index": 52}, {"name": "Require(46) node_modules/egg/agent.js", "start": 1748248475218, "end": 1748248475219, "duration": 1, "pid": 31448, "index": 53}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748248475231, "end": 1748248476657, "duration": 1426, "pid": 31448, "index": 54}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1748248475232, "end": 1748248476598, "duration": 1366, "pid": 31448, "index": 55}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1748248475233, "end": 1748248477007, "duration": 1774, "pid": 31448, "index": 56}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748248476194, "end": 1748248476400, "duration": 206, "pid": 31448, "index": 57}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748248476245, "end": 1748248476652, "duration": 407, "pid": 31448, "index": 58}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748248476457, "end": 1748248476955, "duration": 498, "pid": 31448, "index": 59}]