[{"name": "Process Start", "start": 1748254711807, "end": 1748254714350, "duration": 2543, "pid": 17064, "index": 0}, {"name": "Application Start", "start": 1748254714358, "end": 1748254717118, "duration": 2760, "pid": 17064, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748254714406, "end": 1748254714479, "duration": 73, "pid": 17064, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748254714479, "end": 1748254714557, "duration": 78, "pid": 17064, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748254714480, "end": 1748254714481, "duration": 1, "pid": 17064, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748254714485, "end": 1748254714486, "duration": 1, "pid": 17064, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748254714488, "end": 1748254714489, "duration": 1, "pid": 17064, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748254714490, "end": 1748254714491, "duration": 1, "pid": 17064, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748254714502, "end": 1748254714503, "duration": 1, "pid": 17064, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748254714504, "end": 1748254714505, "duration": 1, "pid": 17064, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748254714506, "end": 1748254714506, "duration": 0, "pid": 17064, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748254714507, "end": 1748254714508, "duration": 1, "pid": 17064, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748254714509, "end": 1748254714509, "duration": 0, "pid": 17064, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748254714511, "end": 1748254714513, "duration": 2, "pid": 17064, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748254714515, "end": 1748254714516, "duration": 1, "pid": 17064, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748254714517, "end": 1748254714518, "duration": 1, "pid": 17064, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748254714519, "end": 1748254714520, "duration": 1, "pid": 17064, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748254714521, "end": 1748254714521, "duration": 0, "pid": 17064, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748254714522, "end": 1748254714523, "duration": 1, "pid": 17064, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748254714524, "end": 1748254714525, "duration": 1, "pid": 17064, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748254714525, "end": 1748254714527, "duration": 2, "pid": 17064, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748254714529, "end": 1748254714530, "duration": 1, "pid": 17064, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748254714531, "end": 1748254714532, "duration": 1, "pid": 17064, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748254714533, "end": 1748254714534, "duration": 1, "pid": 17064, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748254714535, "end": 1748254714535, "duration": 0, "pid": 17064, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748254714538, "end": 1748254714538, "duration": 0, "pid": 17064, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748254714540, "end": 1748254714541, "duration": 1, "pid": 17064, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748254714546, "end": 1748254714547, "duration": 1, "pid": 17064, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748254714551, "end": 1748254714552, "duration": 1, "pid": 17064, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748254714556, "end": 1748254714556, "duration": 0, "pid": 17064, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748254714557, "end": 1748254714557, "duration": 0, "pid": 17064, "index": 30}, {"name": "Load extend/application.js", "start": 1748254714561, "end": 1748254714728, "duration": 167, "pid": 17064, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748254714563, "end": 1748254714564, "duration": 1, "pid": 17064, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748254714565, "end": 1748254714569, "duration": 4, "pid": 17064, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748254714571, "end": 1748254714585, "duration": 14, "pid": 17064, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748254714587, "end": 1748254714599, "duration": 12, "pid": 17064, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748254714602, "end": 1748254714605, "duration": 3, "pid": 17064, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748254714608, "end": 1748254714613, "duration": 5, "pid": 17064, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748254714614, "end": 1748254714718, "duration": 104, "pid": 17064, "index": 38}, {"name": "Load extend/request.js", "start": 1748254714728, "end": 1748254714749, "duration": 21, "pid": 17064, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748254714736, "end": 1748254714738, "duration": 2, "pid": 17064, "index": 40}, {"name": "Load extend/response.js", "start": 1748254714749, "end": 1748254714774, "duration": 25, "pid": 17064, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748254714757, "end": 1748254714765, "duration": 8, "pid": 17064, "index": 42}, {"name": "Load extend/context.js", "start": 1748254714774, "end": 1748254714864, "duration": 90, "pid": 17064, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748254714775, "end": 1748254714800, "duration": 25, "pid": 17064, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748254714801, "end": 1748254714804, "duration": 3, "pid": 17064, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748254714805, "end": 1748254714806, "duration": 1, "pid": 17064, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748254714807, "end": 1748254714846, "duration": 39, "pid": 17064, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748254714848, "end": 1748254714849, "duration": 1, "pid": 17064, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748254714851, "end": 1748254714851, "duration": 0, "pid": 17064, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748254714853, "end": 1748254714856, "duration": 3, "pid": 17064, "index": 50}, {"name": "Load extend/helper.js", "start": 1748254714864, "end": 1748254714917, "duration": 53, "pid": 17064, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748254714865, "end": 1748254714898, "duration": 33, "pid": 17064, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748254714905, "end": 1748254714905, "duration": 0, "pid": 17064, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748254714906, "end": 1748254714907, "duration": 1, "pid": 17064, "index": 54}, {"name": "Load app.js", "start": 1748254714918, "end": 1748254715046, "duration": 128, "pid": 17064, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748254714919, "end": 1748254714920, "duration": 1, "pid": 17064, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748254714921, "end": 1748254714924, "duration": 3, "pid": 17064, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748254714925, "end": 1748254714946, "duration": 21, "pid": 17064, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748254714946, "end": 1748254714968, "duration": 22, "pid": 17064, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748254714969, "end": 1748254714988, "duration": 19, "pid": 17064, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748254714989, "end": 1748254714990, "duration": 1, "pid": 17064, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748254714990, "end": 1748254714993, "duration": 3, "pid": 17064, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748254714994, "end": 1748254714995, "duration": 1, "pid": 17064, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748254714996, "end": 1748254714996, "duration": 0, "pid": 17064, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748254714997, "end": 1748254714998, "duration": 1, "pid": 17064, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748254715000, "end": 1748254715000, "duration": 0, "pid": 17064, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748254715000, "end": 1748254715001, "duration": 1, "pid": 17064, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748254715002, "end": 1748254715002, "duration": 0, "pid": 17064, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748254715003, "end": 1748254715005, "duration": 2, "pid": 17064, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748254715006, "end": 1748254715032, "duration": 26, "pid": 17064, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748254715033, "end": 1748254715042, "duration": 9, "pid": 17064, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748254715063, "end": 1748254717083, "duration": 2020, "pid": 17064, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748254715996, "end": 1748254716165, "duration": 169, "pid": 17064, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748254716031, "end": 1748254716992, "duration": 961, "pid": 17064, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748254716222, "end": 1748254717118, "duration": 896, "pid": 17064, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748254716391, "end": 1748254717090, "duration": 699, "pid": 17064, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748254716518, "end": 1748254717019, "duration": 501, "pid": 17064, "index": 77}, {"name": "Load Service", "start": 1748254716518, "end": 1748254716657, "duration": 139, "pid": 17064, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748254716518, "end": 1748254716657, "duration": 139, "pid": 17064, "index": 79}, {"name": "Load Middleware", "start": 1748254716658, "end": 1748254716855, "duration": 197, "pid": 17064, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748254716658, "end": 1748254716838, "duration": 180, "pid": 17064, "index": 81}, {"name": "Load Controller", "start": 1748254716855, "end": 1748254716914, "duration": 59, "pid": 17064, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748254716855, "end": 1748254716914, "duration": 59, "pid": 17064, "index": 83}, {"name": "Load Router", "start": 1748254716914, "end": 1748254716922, "duration": 8, "pid": 17064, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748254716915, "end": 1748254716916, "duration": 1, "pid": 17064, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748254716917, "end": 1748254716992, "duration": 75, "pid": 17064, "index": 86}]