[{"name": "Process Start", "start": 1748254717437, "end": 1748254719654, "duration": 2217, "pid": 14832, "index": 0}, {"name": "Application Start", "start": 1748254719656, "end": 1748254722183, "duration": 2527, "pid": 14832, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748254719680, "end": 1748254719727, "duration": 47, "pid": 14832, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748254719727, "end": 1748254719784, "duration": 57, "pid": 14832, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748254719728, "end": 1748254719729, "duration": 1, "pid": 14832, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748254719731, "end": 1748254719732, "duration": 1, "pid": 14832, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748254719733, "end": 1748254719733, "duration": 0, "pid": 14832, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748254719734, "end": 1748254719735, "duration": 1, "pid": 14832, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748254719736, "end": 1748254719737, "duration": 1, "pid": 14832, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748254719738, "end": 1748254719738, "duration": 0, "pid": 14832, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748254719739, "end": 1748254719740, "duration": 1, "pid": 14832, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748254719740, "end": 1748254719741, "duration": 1, "pid": 14832, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748254719742, "end": 1748254719742, "duration": 0, "pid": 14832, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748254719744, "end": 1748254719745, "duration": 1, "pid": 14832, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748254719746, "end": 1748254719746, "duration": 0, "pid": 14832, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748254719748, "end": 1748254719749, "duration": 1, "pid": 14832, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748254719750, "end": 1748254719751, "duration": 1, "pid": 14832, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748254719751, "end": 1748254719752, "duration": 1, "pid": 14832, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748254719753, "end": 1748254719754, "duration": 1, "pid": 14832, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748254719754, "end": 1748254719755, "duration": 1, "pid": 14832, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748254719755, "end": 1748254719756, "duration": 1, "pid": 14832, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748254719757, "end": 1748254719757, "duration": 0, "pid": 14832, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748254719759, "end": 1748254719761, "duration": 2, "pid": 14832, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748254719763, "end": 1748254719764, "duration": 1, "pid": 14832, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748254719765, "end": 1748254719765, "duration": 0, "pid": 14832, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748254719768, "end": 1748254719768, "duration": 0, "pid": 14832, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748254719770, "end": 1748254719770, "duration": 0, "pid": 14832, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748254719772, "end": 1748254719773, "duration": 1, "pid": 14832, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748254719776, "end": 1748254719778, "duration": 2, "pid": 14832, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748254719783, "end": 1748254719784, "duration": 1, "pid": 14832, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748254719784, "end": 1748254719784, "duration": 0, "pid": 14832, "index": 30}, {"name": "Load extend/application.js", "start": 1748254719785, "end": 1748254719910, "duration": 125, "pid": 14832, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748254719787, "end": 1748254719787, "duration": 0, "pid": 14832, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748254719788, "end": 1748254719790, "duration": 2, "pid": 14832, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748254719791, "end": 1748254719800, "duration": 9, "pid": 14832, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748254719802, "end": 1748254719811, "duration": 9, "pid": 14832, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748254719813, "end": 1748254719815, "duration": 2, "pid": 14832, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748254719817, "end": 1748254719820, "duration": 3, "pid": 14832, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748254719821, "end": 1748254719898, "duration": 77, "pid": 14832, "index": 38}, {"name": "Load extend/request.js", "start": 1748254719910, "end": 1748254719932, "duration": 22, "pid": 14832, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748254719919, "end": 1748254719922, "duration": 3, "pid": 14832, "index": 40}, {"name": "Load extend/response.js", "start": 1748254719932, "end": 1748254719954, "duration": 22, "pid": 14832, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748254719941, "end": 1748254719946, "duration": 5, "pid": 14832, "index": 42}, {"name": "Load extend/context.js", "start": 1748254719954, "end": 1748254720056, "duration": 102, "pid": 14832, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748254719955, "end": 1748254719981, "duration": 26, "pid": 14832, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748254719982, "end": 1748254719986, "duration": 4, "pid": 14832, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748254719988, "end": 1748254719988, "duration": 0, "pid": 14832, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748254719990, "end": 1748254720034, "duration": 44, "pid": 14832, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748254720035, "end": 1748254720037, "duration": 2, "pid": 14832, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748254720039, "end": 1748254720040, "duration": 1, "pid": 14832, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748254720042, "end": 1748254720047, "duration": 5, "pid": 14832, "index": 50}, {"name": "Load extend/helper.js", "start": 1748254720056, "end": 1748254720120, "duration": 64, "pid": 14832, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748254720057, "end": 1748254720100, "duration": 43, "pid": 14832, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748254720108, "end": 1748254720109, "duration": 1, "pid": 14832, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748254720110, "end": 1748254720111, "duration": 1, "pid": 14832, "index": 54}, {"name": "Load app.js", "start": 1748254720120, "end": 1748254720242, "duration": 122, "pid": 14832, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748254720121, "end": 1748254720122, "duration": 1, "pid": 14832, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748254720123, "end": 1748254720127, "duration": 4, "pid": 14832, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748254720129, "end": 1748254720150, "duration": 21, "pid": 14832, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748254720150, "end": 1748254720172, "duration": 22, "pid": 14832, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748254720173, "end": 1748254720192, "duration": 19, "pid": 14832, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748254720193, "end": 1748254720194, "duration": 1, "pid": 14832, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748254720194, "end": 1748254720197, "duration": 3, "pid": 14832, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748254720197, "end": 1748254720198, "duration": 1, "pid": 14832, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748254720198, "end": 1748254720199, "duration": 1, "pid": 14832, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748254720199, "end": 1748254720200, "duration": 1, "pid": 14832, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748254720201, "end": 1748254720201, "duration": 0, "pid": 14832, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748254720202, "end": 1748254720202, "duration": 0, "pid": 14832, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748254720202, "end": 1748254720203, "duration": 1, "pid": 14832, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748254720203, "end": 1748254720210, "duration": 7, "pid": 14832, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748254720210, "end": 1748254720234, "duration": 24, "pid": 14832, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748254720234, "end": 1748254720240, "duration": 6, "pid": 14832, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748254720257, "end": 1748254722153, "duration": 1896, "pid": 14832, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748254721217, "end": 1748254721314, "duration": 97, "pid": 14832, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748254721245, "end": 1748254722067, "duration": 822, "pid": 14832, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748254721349, "end": 1748254722182, "duration": 833, "pid": 14832, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748254721452, "end": 1748254722150, "duration": 698, "pid": 14832, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748254721569, "end": 1748254722101, "duration": 532, "pid": 14832, "index": 77}, {"name": "Load Service", "start": 1748254721570, "end": 1748254721750, "duration": 180, "pid": 14832, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748254721570, "end": 1748254721750, "duration": 180, "pid": 14832, "index": 79}, {"name": "Load Middleware", "start": 1748254721750, "end": 1748254721948, "duration": 198, "pid": 14832, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748254721750, "end": 1748254721931, "duration": 181, "pid": 14832, "index": 81}, {"name": "Load Controller", "start": 1748254721948, "end": 1748254721998, "duration": 50, "pid": 14832, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748254721949, "end": 1748254721998, "duration": 49, "pid": 14832, "index": 83}, {"name": "Load Router", "start": 1748254721998, "end": 1748254722004, "duration": 6, "pid": 14832, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748254721998, "end": 1748254721999, "duration": 1, "pid": 14832, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748254722000, "end": 1748254722067, "duration": 67, "pid": 14832, "index": 86}]