[{"name": "Process Start", "start": 1748253132666, "end": 1748253135278, "duration": 2612, "pid": 27056, "index": 0}, {"name": "Application Start", "start": 1748253135281, "end": 1748253139849, "duration": 4568, "pid": 27056, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748253135331, "end": 1748253135399, "duration": 68, "pid": 27056, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748253135399, "end": 1748253135482, "duration": 83, "pid": 27056, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748253135400, "end": 1748253135401, "duration": 1, "pid": 27056, "index": 4}, {"name": "Require(1) config/config.prod.js", "start": 1748253135405, "end": 1748253135406, "duration": 1, "pid": 27056, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748253135407, "end": 1748253135408, "duration": 1, "pid": 27056, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748253135409, "end": 1748253135409, "duration": 0, "pid": 27056, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748253135412, "end": 1748253135412, "duration": 0, "pid": 27056, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748253135413, "end": 1748253135414, "duration": 1, "pid": 27056, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748253135428, "end": 1748253135429, "duration": 1, "pid": 27056, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748253135431, "end": 1748253135432, "duration": 1, "pid": 27056, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748253135434, "end": 1748253135435, "duration": 1, "pid": 27056, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748253135437, "end": 1748253135438, "duration": 1, "pid": 27056, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1748253135439, "end": 1748253135440, "duration": 1, "pid": 27056, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1748253135441, "end": 1748253135442, "duration": 1, "pid": 27056, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1748253135443, "end": 1748253135444, "duration": 1, "pid": 27056, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1748253135445, "end": 1748253135446, "duration": 1, "pid": 27056, "index": 17}, {"name": "Require(14) node_modules/egg-jwt/config/config.default.js", "start": 1748253135447, "end": 1748253135448, "duration": 1, "pid": 27056, "index": 18}, {"name": "Require(15) node_modules/egg-cors/config/config.default.js", "start": 1748253135449, "end": 1748253135450, "duration": 1, "pid": 27056, "index": 19}, {"name": "Require(16) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748253135451, "end": 1748253135451, "duration": 0, "pid": 27056, "index": 20}, {"name": "Require(17) node_modules/egg-mysql/config/config.default.js", "start": 1748253135452, "end": 1748253135453, "duration": 1, "pid": 27056, "index": 21}, {"name": "Require(18) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748253135455, "end": 1748253135455, "duration": 0, "pid": 27056, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748253135456, "end": 1748253135457, "duration": 1, "pid": 27056, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748253135459, "end": 1748253135459, "duration": 0, "pid": 27056, "index": 24}, {"name": "Require(21) node_modules/egg-static/config/config.prod.js", "start": 1748253135475, "end": 1748253135476, "duration": 1, "pid": 27056, "index": 25}, {"name": "Require(22) config/config.prod.js", "start": 1748253135482, "end": 1748253135482, "duration": 0, "pid": 27056, "index": 26}, {"name": "Load extend/application.js", "start": 1748253135484, "end": 1748253135678, "duration": 194, "pid": 27056, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1748253135486, "end": 1748253135489, "duration": 3, "pid": 27056, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1748253135491, "end": 1748253135494, "duration": 3, "pid": 27056, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1748253135495, "end": 1748253135508, "duration": 13, "pid": 27056, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1748253135511, "end": 1748253135525, "duration": 14, "pid": 27056, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1748253135527, "end": 1748253135530, "duration": 3, "pid": 27056, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1748253135531, "end": 1748253135535, "duration": 4, "pid": 27056, "index": 33}, {"name": "Require(29) node_modules/egg-jwt/app/extend/application.js", "start": 1748253135537, "end": 1748253135665, "duration": 128, "pid": 27056, "index": 34}, {"name": "Load extend/request.js", "start": 1748253135678, "end": 1748253135764, "duration": 86, "pid": 27056, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1748253135713, "end": 1748253135718, "duration": 5, "pid": 27056, "index": 36}, {"name": "Load extend/response.js", "start": 1748253135765, "end": 1748253135854, "duration": 89, "pid": 27056, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1748253135824, "end": 1748253135836, "duration": 12, "pid": 27056, "index": 38}, {"name": "Load extend/context.js", "start": 1748253135854, "end": 1748253136006, "duration": 152, "pid": 27056, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1748253135856, "end": 1748253135889, "duration": 33, "pid": 27056, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1748253135889, "end": 1748253135893, "duration": 4, "pid": 27056, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1748253135894, "end": 1748253135895, "duration": 1, "pid": 27056, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1748253135905, "end": 1748253135966, "duration": 61, "pid": 27056, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1748253135968, "end": 1748253135971, "duration": 3, "pid": 27056, "index": 44}, {"name": "Require(37) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748253135990, "end": 1748253135991, "duration": 1, "pid": 27056, "index": 45}, {"name": "Require(38) node_modules/egg/app/extend/context.js", "start": 1748253135993, "end": 1748253135997, "duration": 4, "pid": 27056, "index": 46}, {"name": "Load extend/helper.js", "start": 1748253136006, "end": 1748253136135, "duration": 129, "pid": 27056, "index": 47}, {"name": "Require(39) node_modules/egg-security/app/extend/helper.js", "start": 1748253136008, "end": 1748253136087, "duration": 79, "pid": 27056, "index": 48}, {"name": "Require(40) node_modules/egg/app/extend/helper.js", "start": 1748253136105, "end": 1748253136105, "duration": 0, "pid": 27056, "index": 49}, {"name": "Require(41) app/extend/helper.js", "start": 1748253136109, "end": 1748253136110, "duration": 1, "pid": 27056, "index": 50}, {"name": "Load app.js", "start": 1748253136136, "end": 1748253136336, "duration": 200, "pid": 27056, "index": 51}, {"name": "Require(42) node_modules/egg-session/app.js", "start": 1748253136149, "end": 1748253136156, "duration": 7, "pid": 27056, "index": 52}, {"name": "Require(43) node_modules/egg-security/app.js", "start": 1748253136158, "end": 1748253136162, "duration": 4, "pid": 27056, "index": 53}, {"name": "Require(44) node_modules/egg-onerror/app.js", "start": 1748253136165, "end": 1748253136196, "duration": 31, "pid": 27056, "index": 54}, {"name": "Require(45) node_modules/egg-i18n/app.js", "start": 1748253136197, "end": 1748253136229, "duration": 32, "pid": 27056, "index": 55}, {"name": "Require(46) node_modules/egg-watcher/app.js", "start": 1748253136230, "end": 1748253136260, "duration": 30, "pid": 27056, "index": 56}, {"name": "Require(47) node_modules/egg-schedule/app.js", "start": 1748253136261, "end": 1748253136262, "duration": 1, "pid": 27056, "index": 57}, {"name": "Require(48) node_modules/egg-multipart/app.js", "start": 1748253136262, "end": 1748253136267, "duration": 5, "pid": 27056, "index": 58}, {"name": "Require(49) node_modules/egg-logrotator/app.js", "start": 1748253136269, "end": 1748253136269, "duration": 0, "pid": 27056, "index": 59}, {"name": "Require(50) node_modules/egg-static/app.js", "start": 1748253136270, "end": 1748253136270, "duration": 0, "pid": 27056, "index": 60}, {"name": "Require(51) node_modules/egg-sequelize/app.js", "start": 1748253136273, "end": 1748253136273, "duration": 0, "pid": 27056, "index": 61}, {"name": "Require(52) node_modules/egg-jwt/app.js", "start": 1748253136274, "end": 1748253136275, "duration": 1, "pid": 27056, "index": 62}, {"name": "Require(53) node_modules/egg-cors/app.js", "start": 1748253136276, "end": 1748253136277, "duration": 1, "pid": 27056, "index": 63}, {"name": "Require(54) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748253136278, "end": 1748253136281, "duration": 3, "pid": 27056, "index": 64}, {"name": "Require(55) node_modules/egg-mysql/app.js", "start": 1748253136284, "end": 1748253136317, "duration": 33, "pid": 27056, "index": 65}, {"name": "Require(56) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748253136322, "end": 1748253136333, "duration": 11, "pid": 27056, "index": 66}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748253136357, "end": 1748253139821, "duration": 3464, "pid": 27056, "index": 67}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748253138184, "end": 1748253138329, "duration": 145, "pid": 27056, "index": 68}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748253138230, "end": 1748253139706, "duration": 1476, "pid": 27056, "index": 69}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748253138384, "end": 1748253139847, "duration": 1463, "pid": 27056, "index": 70}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748253138607, "end": 1748253139824, "duration": 1217, "pid": 27056, "index": 71}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748253138815, "end": 1748253139761, "duration": 946, "pid": 27056, "index": 72}, {"name": "Load Service", "start": 1748253138815, "end": 1748253139069, "duration": 254, "pid": 27056, "index": 73}, {"name": "Load \"service\" to Context", "start": 1748253138816, "end": 1748253139069, "duration": 253, "pid": 27056, "index": 74}, {"name": "Load Middleware", "start": 1748253139070, "end": 1748253139431, "duration": 361, "pid": 27056, "index": 75}, {"name": "Load \"middlewares\" to Application", "start": 1748253139070, "end": 1748253139396, "duration": 326, "pid": 27056, "index": 76}, {"name": "Load Controller", "start": 1748253139431, "end": 1748253139528, "duration": 97, "pid": 27056, "index": 77}, {"name": "Load \"controller\" to Application", "start": 1748253139431, "end": 1748253139528, "duration": 97, "pid": 27056, "index": 78}, {"name": "Load Router", "start": 1748253139528, "end": 1748253139542, "duration": 14, "pid": 27056, "index": 79}, {"name": "Require(57) app/router.js", "start": 1748253139529, "end": 1748253139530, "duration": 1, "pid": 27056, "index": 80}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748253139532, "end": 1748253139705, "duration": 173, "pid": 27056, "index": 81}]