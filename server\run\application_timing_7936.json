[{"name": "Process Start", "start": 1748253134886, "end": 1748253137679, "duration": 2793, "pid": 7936, "index": 0}, {"name": "Application Start", "start": 1748253137682, "end": 1748253141243, "duration": 3561, "pid": 7936, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748253137808, "end": 1748253137957, "duration": 149, "pid": 7936, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748253137958, "end": 1748253138066, "duration": 108, "pid": 7936, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748253137965, "end": 1748253137966, "duration": 1, "pid": 7936, "index": 4}, {"name": "Require(1) config/config.prod.js", "start": 1748253137975, "end": 1748253137976, "duration": 1, "pid": 7936, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748253137979, "end": 1748253137980, "duration": 1, "pid": 7936, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748253137981, "end": 1748253137983, "duration": 2, "pid": 7936, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748253137990, "end": 1748253137991, "duration": 1, "pid": 7936, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748253137992, "end": 1748253137993, "duration": 1, "pid": 7936, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748253137994, "end": 1748253137995, "duration": 1, "pid": 7936, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748253137996, "end": 1748253138008, "duration": 12, "pid": 7936, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748253138009, "end": 1748253138010, "duration": 1, "pid": 7936, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748253138011, "end": 1748253138012, "duration": 1, "pid": 7936, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1748253138013, "end": 1748253138014, "duration": 1, "pid": 7936, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1748253138015, "end": 1748253138015, "duration": 0, "pid": 7936, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1748253138017, "end": 1748253138018, "duration": 1, "pid": 7936, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1748253138019, "end": 1748253138021, "duration": 2, "pid": 7936, "index": 17}, {"name": "Require(14) node_modules/egg-jwt/config/config.default.js", "start": 1748253138024, "end": 1748253138025, "duration": 1, "pid": 7936, "index": 18}, {"name": "Require(15) node_modules/egg-cors/config/config.default.js", "start": 1748253138026, "end": 1748253138027, "duration": 1, "pid": 7936, "index": 19}, {"name": "Require(16) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748253138028, "end": 1748253138029, "duration": 1, "pid": 7936, "index": 20}, {"name": "Require(17) node_modules/egg-mysql/config/config.default.js", "start": 1748253138030, "end": 1748253138031, "duration": 1, "pid": 7936, "index": 21}, {"name": "Require(18) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748253138033, "end": 1748253138036, "duration": 3, "pid": 7936, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748253138041, "end": 1748253138042, "duration": 1, "pid": 7936, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748253138045, "end": 1748253138045, "duration": 0, "pid": 7936, "index": 24}, {"name": "Require(21) node_modules/egg-static/config/config.prod.js", "start": 1748253138060, "end": 1748253138060, "duration": 0, "pid": 7936, "index": 25}, {"name": "Require(22) config/config.prod.js", "start": 1748253138066, "end": 1748253138066, "duration": 0, "pid": 7936, "index": 26}, {"name": "Load extend/application.js", "start": 1748253138073, "end": 1748253138271, "duration": 198, "pid": 7936, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1748253138074, "end": 1748253138075, "duration": 1, "pid": 7936, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1748253138076, "end": 1748253138079, "duration": 3, "pid": 7936, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1748253138080, "end": 1748253138098, "duration": 18, "pid": 7936, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1748253138109, "end": 1748253138122, "duration": 13, "pid": 7936, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1748253138124, "end": 1748253138127, "duration": 3, "pid": 7936, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1748253138130, "end": 1748253138138, "duration": 8, "pid": 7936, "index": 33}, {"name": "Require(29) node_modules/egg-jwt/app/extend/application.js", "start": 1748253138140, "end": 1748253138255, "duration": 115, "pid": 7936, "index": 34}, {"name": "Load extend/request.js", "start": 1748253138271, "end": 1748253138316, "duration": 45, "pid": 7936, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1748253138281, "end": 1748253138283, "duration": 2, "pid": 7936, "index": 36}, {"name": "Load extend/response.js", "start": 1748253138316, "end": 1748253138347, "duration": 31, "pid": 7936, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1748253138332, "end": 1748253138337, "duration": 5, "pid": 7936, "index": 38}, {"name": "Load extend/context.js", "start": 1748253138347, "end": 1748253138486, "duration": 139, "pid": 7936, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1748253138349, "end": 1748253138384, "duration": 35, "pid": 7936, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1748253138386, "end": 1748253138394, "duration": 8, "pid": 7936, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1748253138396, "end": 1748253138398, "duration": 2, "pid": 7936, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1748253138400, "end": 1748253138447, "duration": 47, "pid": 7936, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1748253138450, "end": 1748253138453, "duration": 3, "pid": 7936, "index": 44}, {"name": "Require(37) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748253138457, "end": 1748253138458, "duration": 1, "pid": 7936, "index": 45}, {"name": "Require(38) node_modules/egg/app/extend/context.js", "start": 1748253138463, "end": 1748253138472, "duration": 9, "pid": 7936, "index": 46}, {"name": "Load extend/helper.js", "start": 1748253138486, "end": 1748253138558, "duration": 72, "pid": 7936, "index": 47}, {"name": "Require(39) node_modules/egg-security/app/extend/helper.js", "start": 1748253138488, "end": 1748253138535, "duration": 47, "pid": 7936, "index": 48}, {"name": "Require(40) node_modules/egg/app/extend/helper.js", "start": 1748253138545, "end": 1748253138546, "duration": 1, "pid": 7936, "index": 49}, {"name": "Require(41) app/extend/helper.js", "start": 1748253138547, "end": 1748253138548, "duration": 1, "pid": 7936, "index": 50}, {"name": "Load app.js", "start": 1748253138559, "end": 1748253138749, "duration": 190, "pid": 7936, "index": 51}, {"name": "Require(42) node_modules/egg-session/app.js", "start": 1748253138559, "end": 1748253138560, "duration": 1, "pid": 7936, "index": 52}, {"name": "Require(43) node_modules/egg-security/app.js", "start": 1748253138561, "end": 1748253138576, "duration": 15, "pid": 7936, "index": 53}, {"name": "Require(44) node_modules/egg-onerror/app.js", "start": 1748253138578, "end": 1748253138605, "duration": 27, "pid": 7936, "index": 54}, {"name": "Require(45) node_modules/egg-i18n/app.js", "start": 1748253138606, "end": 1748253138639, "duration": 33, "pid": 7936, "index": 55}, {"name": "Require(46) node_modules/egg-watcher/app.js", "start": 1748253138640, "end": 1748253138678, "duration": 38, "pid": 7936, "index": 56}, {"name": "Require(47) node_modules/egg-schedule/app.js", "start": 1748253138680, "end": 1748253138681, "duration": 1, "pid": 7936, "index": 57}, {"name": "Require(48) node_modules/egg-multipart/app.js", "start": 1748253138682, "end": 1748253138686, "duration": 4, "pid": 7936, "index": 58}, {"name": "Require(49) node_modules/egg-logrotator/app.js", "start": 1748253138687, "end": 1748253138688, "duration": 1, "pid": 7936, "index": 59}, {"name": "Require(50) node_modules/egg-static/app.js", "start": 1748253138689, "end": 1748253138689, "duration": 0, "pid": 7936, "index": 60}, {"name": "Require(51) node_modules/egg-sequelize/app.js", "start": 1748253138691, "end": 1748253138691, "duration": 0, "pid": 7936, "index": 61}, {"name": "Require(52) node_modules/egg-jwt/app.js", "start": 1748253138692, "end": 1748253138693, "duration": 1, "pid": 7936, "index": 62}, {"name": "Require(53) node_modules/egg-cors/app.js", "start": 1748253138693, "end": 1748253138694, "duration": 1, "pid": 7936, "index": 63}, {"name": "Require(54) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748253138695, "end": 1748253138701, "duration": 6, "pid": 7936, "index": 64}, {"name": "Require(55) node_modules/egg-mysql/app.js", "start": 1748253138702, "end": 1748253138735, "duration": 33, "pid": 7936, "index": 65}, {"name": "Require(56) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748253138736, "end": 1748253138747, "duration": 11, "pid": 7936, "index": 66}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748253138776, "end": 1748253141228, "duration": 2452, "pid": 7936, "index": 67}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748253140076, "end": 1748253140190, "duration": 114, "pid": 7936, "index": 68}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748253140116, "end": 1748253141131, "duration": 1015, "pid": 7936, "index": 69}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748253140230, "end": 1748253141242, "duration": 1012, "pid": 7936, "index": 70}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748253140345, "end": 1748253141217, "duration": 872, "pid": 7936, "index": 71}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748253140493, "end": 1748253141164, "duration": 671, "pid": 7936, "index": 72}, {"name": "Load Service", "start": 1748253140493, "end": 1748253140692, "duration": 199, "pid": 7936, "index": 73}, {"name": "Load \"service\" to Context", "start": 1748253140493, "end": 1748253140692, "duration": 199, "pid": 7936, "index": 74}, {"name": "Load Middleware", "start": 1748253140693, "end": 1748253140993, "duration": 300, "pid": 7936, "index": 75}, {"name": "Load \"middlewares\" to Application", "start": 1748253140693, "end": 1748253140959, "duration": 266, "pid": 7936, "index": 76}, {"name": "Load Controller", "start": 1748253140994, "end": 1748253141059, "duration": 65, "pid": 7936, "index": 77}, {"name": "Load \"controller\" to Application", "start": 1748253140994, "end": 1748253141059, "duration": 65, "pid": 7936, "index": 78}, {"name": "Load Router", "start": 1748253141059, "end": 1748253141068, "duration": 9, "pid": 7936, "index": 79}, {"name": "Require(57) app/router.js", "start": 1748253141060, "end": 1748253141061, "duration": 1, "pid": 7936, "index": 80}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748253141062, "end": 1748253141131, "duration": 69, "pid": 7936, "index": 81}]