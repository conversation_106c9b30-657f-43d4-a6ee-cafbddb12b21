[{"name": "Process Start", "start": 1748253132212, "end": 1748253134522, "duration": 2310, "pid": 30316, "index": 0}, {"name": "Application Start", "start": 1748253134528, "end": 1748253138786, "duration": 4258, "pid": 30316, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748253134597, "end": 1748253134674, "duration": 77, "pid": 30316, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748253134675, "end": 1748253134759, "duration": 84, "pid": 30316, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748253134676, "end": 1748253134677, "duration": 1, "pid": 30316, "index": 4}, {"name": "Require(1) config/config.prod.js", "start": 1748253134680, "end": 1748253134681, "duration": 1, "pid": 30316, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748253134683, "end": 1748253134684, "duration": 1, "pid": 30316, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748253134685, "end": 1748253134686, "duration": 1, "pid": 30316, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748253134691, "end": 1748253134692, "duration": 1, "pid": 30316, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748253134696, "end": 1748253134697, "duration": 1, "pid": 30316, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748253134701, "end": 1748253134702, "duration": 1, "pid": 30316, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748253134703, "end": 1748253134704, "duration": 1, "pid": 30316, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748253134707, "end": 1748253134708, "duration": 1, "pid": 30316, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748253134710, "end": 1748253134711, "duration": 1, "pid": 30316, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1748253134712, "end": 1748253134713, "duration": 1, "pid": 30316, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1748253134714, "end": 1748253134715, "duration": 1, "pid": 30316, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1748253134716, "end": 1748253134717, "duration": 1, "pid": 30316, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1748253134718, "end": 1748253134720, "duration": 2, "pid": 30316, "index": 17}, {"name": "Require(14) node_modules/egg-jwt/config/config.default.js", "start": 1748253134722, "end": 1748253134723, "duration": 1, "pid": 30316, "index": 18}, {"name": "Require(15) node_modules/egg-cors/config/config.default.js", "start": 1748253134725, "end": 1748253134725, "duration": 0, "pid": 30316, "index": 19}, {"name": "Require(16) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748253134726, "end": 1748253134729, "duration": 3, "pid": 30316, "index": 20}, {"name": "Require(17) node_modules/egg-mysql/config/config.default.js", "start": 1748253134730, "end": 1748253134731, "duration": 1, "pid": 30316, "index": 21}, {"name": "Require(18) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748253134732, "end": 1748253134733, "duration": 1, "pid": 30316, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748253134734, "end": 1748253134735, "duration": 1, "pid": 30316, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748253134741, "end": 1748253134741, "duration": 0, "pid": 30316, "index": 24}, {"name": "Require(21) node_modules/egg-static/config/config.prod.js", "start": 1748253134749, "end": 1748253134750, "duration": 1, "pid": 30316, "index": 25}, {"name": "Require(22) config/config.prod.js", "start": 1748253134758, "end": 1748253134758, "duration": 0, "pid": 30316, "index": 26}, {"name": "Load extend/application.js", "start": 1748253134763, "end": 1748253135045, "duration": 282, "pid": 30316, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1748253134765, "end": 1748253134766, "duration": 1, "pid": 30316, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1748253134768, "end": 1748253134776, "duration": 8, "pid": 30316, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1748253134777, "end": 1748253134793, "duration": 16, "pid": 30316, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1748253134796, "end": 1748253134811, "duration": 15, "pid": 30316, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1748253134828, "end": 1748253134836, "duration": 8, "pid": 30316, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1748253134840, "end": 1748253134844, "duration": 4, "pid": 30316, "index": 33}, {"name": "Require(29) node_modules/egg-jwt/app/extend/application.js", "start": 1748253134846, "end": 1748253134994, "duration": 148, "pid": 30316, "index": 34}, {"name": "Load extend/request.js", "start": 1748253135045, "end": 1748253135079, "duration": 34, "pid": 30316, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1748253135059, "end": 1748253135063, "duration": 4, "pid": 30316, "index": 36}, {"name": "Load extend/response.js", "start": 1748253135079, "end": 1748253135127, "duration": 48, "pid": 30316, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1748253135092, "end": 1748253135100, "duration": 8, "pid": 30316, "index": 38}, {"name": "Load extend/context.js", "start": 1748253135127, "end": 1748253135271, "duration": 144, "pid": 30316, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1748253135129, "end": 1748253135174, "duration": 45, "pid": 30316, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1748253135174, "end": 1748253135181, "duration": 7, "pid": 30316, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1748253135183, "end": 1748253135184, "duration": 1, "pid": 30316, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1748253135186, "end": 1748253135241, "duration": 55, "pid": 30316, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1748253135243, "end": 1748253135245, "duration": 2, "pid": 30316, "index": 44}, {"name": "Require(37) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748253135247, "end": 1748253135248, "duration": 1, "pid": 30316, "index": 45}, {"name": "Require(38) node_modules/egg/app/extend/context.js", "start": 1748253135250, "end": 1748253135256, "duration": 6, "pid": 30316, "index": 46}, {"name": "Load extend/helper.js", "start": 1748253135271, "end": 1748253135347, "duration": 76, "pid": 30316, "index": 47}, {"name": "Require(39) node_modules/egg-security/app/extend/helper.js", "start": 1748253135273, "end": 1748253135321, "duration": 48, "pid": 30316, "index": 48}, {"name": "Require(40) node_modules/egg/app/extend/helper.js", "start": 1748253135329, "end": 1748253135330, "duration": 1, "pid": 30316, "index": 49}, {"name": "Require(41) app/extend/helper.js", "start": 1748253135331, "end": 1748253135332, "duration": 1, "pid": 30316, "index": 50}, {"name": "Load app.js", "start": 1748253135347, "end": 1748253135515, "duration": 168, "pid": 30316, "index": 51}, {"name": "Require(42) node_modules/egg-session/app.js", "start": 1748253135350, "end": 1748253135351, "duration": 1, "pid": 30316, "index": 52}, {"name": "Require(43) node_modules/egg-security/app.js", "start": 1748253135352, "end": 1748253135357, "duration": 5, "pid": 30316, "index": 53}, {"name": "Require(44) node_modules/egg-onerror/app.js", "start": 1748253135359, "end": 1748253135384, "duration": 25, "pid": 30316, "index": 54}, {"name": "Require(45) node_modules/egg-i18n/app.js", "start": 1748253135385, "end": 1748253135412, "duration": 27, "pid": 30316, "index": 55}, {"name": "Require(46) node_modules/egg-watcher/app.js", "start": 1748253135413, "end": 1748253135451, "duration": 38, "pid": 30316, "index": 56}, {"name": "Require(47) node_modules/egg-schedule/app.js", "start": 1748253135451, "end": 1748253135453, "duration": 2, "pid": 30316, "index": 57}, {"name": "Require(48) node_modules/egg-multipart/app.js", "start": 1748253135454, "end": 1748253135457, "duration": 3, "pid": 30316, "index": 58}, {"name": "Require(49) node_modules/egg-logrotator/app.js", "start": 1748253135458, "end": 1748253135459, "duration": 1, "pid": 30316, "index": 59}, {"name": "Require(50) node_modules/egg-static/app.js", "start": 1748253135459, "end": 1748253135460, "duration": 1, "pid": 30316, "index": 60}, {"name": "Require(51) node_modules/egg-sequelize/app.js", "start": 1748253135462, "end": 1748253135462, "duration": 0, "pid": 30316, "index": 61}, {"name": "Require(52) node_modules/egg-jwt/app.js", "start": 1748253135463, "end": 1748253135464, "duration": 1, "pid": 30316, "index": 62}, {"name": "Require(53) node_modules/egg-cors/app.js", "start": 1748253135465, "end": 1748253135465, "duration": 0, "pid": 30316, "index": 63}, {"name": "Require(54) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748253135466, "end": 1748253135471, "duration": 5, "pid": 30316, "index": 64}, {"name": "Require(55) node_modules/egg-mysql/app.js", "start": 1748253135472, "end": 1748253135497, "duration": 25, "pid": 30316, "index": 65}, {"name": "Require(56) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748253135498, "end": 1748253135513, "duration": 15, "pid": 30316, "index": 66}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748253135536, "end": 1748253138762, "duration": 3226, "pid": 30316, "index": 67}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748253136988, "end": 1748253137126, "duration": 138, "pid": 30316, "index": 68}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748253137026, "end": 1748253138633, "duration": 1607, "pid": 30316, "index": 69}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748253137180, "end": 1748253138786, "duration": 1606, "pid": 30316, "index": 70}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748253137408, "end": 1748253138753, "duration": 1345, "pid": 30316, "index": 71}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748253137717, "end": 1748253138690, "duration": 973, "pid": 30316, "index": 72}, {"name": "Load Service", "start": 1748253137717, "end": 1748253138148, "duration": 431, "pid": 30316, "index": 73}, {"name": "Load \"service\" to Context", "start": 1748253137722, "end": 1748253138148, "duration": 426, "pid": 30316, "index": 74}, {"name": "Load Middleware", "start": 1748253138148, "end": 1748253138419, "duration": 271, "pid": 30316, "index": 75}, {"name": "Load \"middlewares\" to Application", "start": 1748253138148, "end": 1748253138391, "duration": 243, "pid": 30316, "index": 76}, {"name": "Load Controller", "start": 1748253138419, "end": 1748253138521, "duration": 102, "pid": 30316, "index": 77}, {"name": "Load \"controller\" to Application", "start": 1748253138419, "end": 1748253138521, "duration": 102, "pid": 30316, "index": 78}, {"name": "Load Router", "start": 1748253138522, "end": 1748253138536, "duration": 14, "pid": 30316, "index": 79}, {"name": "Require(57) app/router.js", "start": 1748253138522, "end": 1748253138524, "duration": 2, "pid": 30316, "index": 80}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748253138528, "end": 1748253138633, "duration": 105, "pid": 30316, "index": 81}]