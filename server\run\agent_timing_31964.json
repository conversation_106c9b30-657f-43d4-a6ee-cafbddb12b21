[{"name": "Process Start", "start": 1748253031969, "end": 1748253033431, "duration": 1462, "pid": 31964, "index": 0}, {"name": "Application Start", "start": 1748253033433, "end": 1748253034836, "duration": 1403, "pid": 31964, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748253033458, "end": 1748253033501, "duration": 43, "pid": 31964, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748253033502, "end": 1748253033548, "duration": 46, "pid": 31964, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748253033502, "end": 1748253033504, "duration": 2, "pid": 31964, "index": 4}, {"name": "Require(1) config/config.prod.js", "start": 1748253033506, "end": 1748253033507, "duration": 1, "pid": 31964, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748253033508, "end": 1748253033508, "duration": 0, "pid": 31964, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748253033509, "end": 1748253033510, "duration": 1, "pid": 31964, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748253033511, "end": 1748253033512, "duration": 1, "pid": 31964, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748253033513, "end": 1748253033514, "duration": 1, "pid": 31964, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748253033515, "end": 1748253033516, "duration": 1, "pid": 31964, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748253033516, "end": 1748253033517, "duration": 1, "pid": 31964, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748253033518, "end": 1748253033518, "duration": 0, "pid": 31964, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748253033519, "end": 1748253033520, "duration": 1, "pid": 31964, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1748253033521, "end": 1748253033522, "duration": 1, "pid": 31964, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1748253033522, "end": 1748253033523, "duration": 1, "pid": 31964, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1748253033524, "end": 1748253033525, "duration": 1, "pid": 31964, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1748253033526, "end": 1748253033527, "duration": 1, "pid": 31964, "index": 17}, {"name": "Require(14) node_modules/egg-jwt/config/config.default.js", "start": 1748253033529, "end": 1748253033529, "duration": 0, "pid": 31964, "index": 18}, {"name": "Require(15) node_modules/egg-cors/config/config.default.js", "start": 1748253033530, "end": 1748253033531, "duration": 1, "pid": 31964, "index": 19}, {"name": "Require(16) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748253033531, "end": 1748253033532, "duration": 1, "pid": 31964, "index": 20}, {"name": "Require(17) node_modules/egg-mysql/config/config.default.js", "start": 1748253033533, "end": 1748253033533, "duration": 0, "pid": 31964, "index": 21}, {"name": "Require(18) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748253033534, "end": 1748253033535, "duration": 1, "pid": 31964, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748253033536, "end": 1748253033536, "duration": 0, "pid": 31964, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748253033539, "end": 1748253033539, "duration": 0, "pid": 31964, "index": 24}, {"name": "Require(21) node_modules/egg-static/config/config.prod.js", "start": 1748253033544, "end": 1748253033544, "duration": 0, "pid": 31964, "index": 25}, {"name": "Require(22) config/config.prod.js", "start": 1748253033548, "end": 1748253033548, "duration": 0, "pid": 31964, "index": 26}, {"name": "Load extend/agent.js", "start": 1748253033549, "end": 1748253033661, "duration": 112, "pid": 31964, "index": 27}, {"name": "Require(23) node_modules/egg-security/app/extend/agent.js", "start": 1748253033551, "end": 1748253033553, "duration": 2, "pid": 31964, "index": 28}, {"name": "Require(24) node_modules/egg-schedule/app/extend/agent.js", "start": 1748253033555, "end": 1748253033646, "duration": 91, "pid": 31964, "index": 29}, {"name": "Require(25) node_modules/egg-logrotator/app/extend/agent.js", "start": 1748253033647, "end": 1748253033650, "duration": 3, "pid": 31964, "index": 30}, {"name": "Load extend/context.js", "start": 1748253033661, "end": 1748253033747, "duration": 86, "pid": 31964, "index": 31}, {"name": "Require(26) node_modules/egg-security/app/extend/context.js", "start": 1748253033662, "end": 1748253033684, "duration": 22, "pid": 31964, "index": 32}, {"name": "Require(27) node_modules/egg-jsonp/app/extend/context.js", "start": 1748253033685, "end": 1748253033690, "duration": 5, "pid": 31964, "index": 33}, {"name": "Require(28) node_modules/egg-i18n/app/extend/context.js", "start": 1748253033691, "end": 1748253033691, "duration": 0, "pid": 31964, "index": 34}, {"name": "Require(29) node_modules/egg-multipart/app/extend/context.js", "start": 1748253033693, "end": 1748253033727, "duration": 34, "pid": 31964, "index": 35}, {"name": "Require(30) node_modules/egg-view/app/extend/context.js", "start": 1748253033730, "end": 1748253033732, "duration": 2, "pid": 31964, "index": 36}, {"name": "Require(31) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748253033734, "end": 1748253033735, "duration": 1, "pid": 31964, "index": 37}, {"name": "Require(32) node_modules/egg/app/extend/context.js", "start": 1748253033736, "end": 1748253033739, "duration": 3, "pid": 31964, "index": 38}, {"name": "Load agent.js", "start": 1748253033747, "end": 1748253033807, "duration": 60, "pid": 31964, "index": 39}, {"name": "Require(33) node_modules/egg-security/agent.js", "start": 1748253033749, "end": 1748253033749, "duration": 0, "pid": 31964, "index": 40}, {"name": "Require(34) node_modules/egg-onerror/agent.js", "start": 1748253033750, "end": 1748253033750, "duration": 0, "pid": 31964, "index": 41}, {"name": "Require(35) node_modules/egg-watcher/agent.js", "start": 1748253033751, "end": 1748253033767, "duration": 16, "pid": 31964, "index": 42}, {"name": "Require(36) node_modules/egg-schedule/agent.js", "start": 1748253033768, "end": 1748253033770, "duration": 2, "pid": 31964, "index": 43}, {"name": "Require(37) node_modules/egg-logrotator/agent.js", "start": 1748253033772, "end": 1748253033772, "duration": 0, "pid": 31964, "index": 44}, {"name": "Require(38) node_modules/egg-sequelize/agent.js", "start": 1748253033773, "end": 1748253033774, "duration": 1, "pid": 31964, "index": 45}, {"name": "Require(39) node_modules/egg-mysql/agent.js", "start": 1748253033775, "end": 1748253033798, "duration": 23, "pid": 31964, "index": 46}, {"name": "Require(40) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1748253033799, "end": 1748253033805, "duration": 6, "pid": 31964, "index": 47}, {"name": "Require(41) node_modules/egg/agent.js", "start": 1748253033806, "end": 1748253033807, "duration": 1, "pid": 31964, "index": 48}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748253033813, "end": 1748253034749, "duration": 936, "pid": 31964, "index": 49}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1748253033814, "end": 1748253034738, "duration": 924, "pid": 31964, "index": 50}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748253034485, "end": 1748253034605, "duration": 120, "pid": 31964, "index": 51}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748253034516, "end": 1748253034747, "duration": 231, "pid": 31964, "index": 52}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748253034645, "end": 1748253034836, "duration": 191, "pid": 31964, "index": 53}]