[{"name": "Process Start", "start": 1748248487412, "end": 1748248489947, "duration": 2535, "pid": 21388, "index": 0}, {"name": "Application Start", "start": 1748248489949, "end": 1748248492233, "duration": 2284, "pid": 21388, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748248489973, "end": 1748248490015, "duration": 42, "pid": 21388, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748248490016, "end": 1748248490060, "duration": 44, "pid": 21388, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748248490017, "end": 1748248490017, "duration": 0, "pid": 21388, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748248490020, "end": 1748248490020, "duration": 0, "pid": 21388, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748248490021, "end": 1748248490022, "duration": 1, "pid": 21388, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748248490022, "end": 1748248490023, "duration": 1, "pid": 21388, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748248490024, "end": 1748248490025, "duration": 1, "pid": 21388, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748248490025, "end": 1748248490026, "duration": 1, "pid": 21388, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748248490027, "end": 1748248490027, "duration": 0, "pid": 21388, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748248490028, "end": 1748248490029, "duration": 1, "pid": 21388, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748248490031, "end": 1748248490031, "duration": 0, "pid": 21388, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748248490032, "end": 1748248490033, "duration": 1, "pid": 21388, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748248490034, "end": 1748248490034, "duration": 0, "pid": 21388, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748248490035, "end": 1748248490036, "duration": 1, "pid": 21388, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748248490036, "end": 1748248490037, "duration": 1, "pid": 21388, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748248490038, "end": 1748248490038, "duration": 0, "pid": 21388, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748248490039, "end": 1748248490039, "duration": 0, "pid": 21388, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748248490040, "end": 1748248490041, "duration": 1, "pid": 21388, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748248490041, "end": 1748248490042, "duration": 1, "pid": 21388, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748248490042, "end": 1748248490043, "duration": 1, "pid": 21388, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748248490043, "end": 1748248490044, "duration": 1, "pid": 21388, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748248490045, "end": 1748248490045, "duration": 0, "pid": 21388, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748248490046, "end": 1748248490047, "duration": 1, "pid": 21388, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748248490049, "end": 1748248490049, "duration": 0, "pid": 21388, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748248490050, "end": 1748248490051, "duration": 1, "pid": 21388, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748248490052, "end": 1748248490053, "duration": 1, "pid": 21388, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748248490056, "end": 1748248490056, "duration": 0, "pid": 21388, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748248490059, "end": 1748248490059, "duration": 0, "pid": 21388, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748248490059, "end": 1748248490059, "duration": 0, "pid": 21388, "index": 30}, {"name": "Load extend/application.js", "start": 1748248490061, "end": 1748248490187, "duration": 126, "pid": 21388, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748248490063, "end": 1748248490064, "duration": 1, "pid": 21388, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748248490064, "end": 1748248490066, "duration": 2, "pid": 21388, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748248490067, "end": 1748248490074, "duration": 7, "pid": 21388, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748248490077, "end": 1748248490089, "duration": 12, "pid": 21388, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748248490090, "end": 1748248490092, "duration": 2, "pid": 21388, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748248490093, "end": 1748248490097, "duration": 4, "pid": 21388, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748248490098, "end": 1748248490175, "duration": 77, "pid": 21388, "index": 38}, {"name": "Load extend/request.js", "start": 1748248490187, "end": 1748248490211, "duration": 24, "pid": 21388, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748248490199, "end": 1748248490201, "duration": 2, "pid": 21388, "index": 40}, {"name": "Load extend/response.js", "start": 1748248490211, "end": 1748248490232, "duration": 21, "pid": 21388, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748248490220, "end": 1748248490224, "duration": 4, "pid": 21388, "index": 42}, {"name": "Load extend/context.js", "start": 1748248490232, "end": 1748248490315, "duration": 83, "pid": 21388, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748248490233, "end": 1748248490254, "duration": 21, "pid": 21388, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748248490255, "end": 1748248490257, "duration": 2, "pid": 21388, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748248490258, "end": 1748248490259, "duration": 1, "pid": 21388, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748248490261, "end": 1748248490295, "duration": 34, "pid": 21388, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748248490297, "end": 1748248490299, "duration": 2, "pid": 21388, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748248490301, "end": 1748248490301, "duration": 0, "pid": 21388, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748248490302, "end": 1748248490306, "duration": 4, "pid": 21388, "index": 50}, {"name": "Load extend/helper.js", "start": 1748248490315, "end": 1748248490368, "duration": 53, "pid": 21388, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748248490316, "end": 1748248490349, "duration": 33, "pid": 21388, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748248490356, "end": 1748248490356, "duration": 0, "pid": 21388, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748248490357, "end": 1748248490358, "duration": 1, "pid": 21388, "index": 54}, {"name": "Load app.js", "start": 1748248490368, "end": 1748248490488, "duration": 120, "pid": 21388, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748248490370, "end": 1748248490371, "duration": 1, "pid": 21388, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748248490372, "end": 1748248490375, "duration": 3, "pid": 21388, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748248490377, "end": 1748248490395, "duration": 18, "pid": 21388, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748248490396, "end": 1748248490417, "duration": 21, "pid": 21388, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748248490417, "end": 1748248490433, "duration": 16, "pid": 21388, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748248490433, "end": 1748248490437, "duration": 4, "pid": 21388, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748248490438, "end": 1748248490442, "duration": 4, "pid": 21388, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748248490442, "end": 1748248490443, "duration": 1, "pid": 21388, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748248490444, "end": 1748248490444, "duration": 0, "pid": 21388, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748248490445, "end": 1748248490445, "duration": 0, "pid": 21388, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748248490447, "end": 1748248490447, "duration": 0, "pid": 21388, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748248490448, "end": 1748248490448, "duration": 0, "pid": 21388, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748248490449, "end": 1748248490450, "duration": 1, "pid": 21388, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748248490451, "end": 1748248490454, "duration": 3, "pid": 21388, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748248490457, "end": 1748248490480, "duration": 23, "pid": 21388, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748248490480, "end": 1748248490486, "duration": 6, "pid": 21388, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748248490502, "end": 1748248492205, "duration": 1703, "pid": 21388, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748248491401, "end": 1748248491482, "duration": 81, "pid": 21388, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748248491427, "end": 1748248492128, "duration": 701, "pid": 21388, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748248491514, "end": 1748248492231, "duration": 717, "pid": 21388, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748248491613, "end": 1748248492211, "duration": 598, "pid": 21388, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748248491706, "end": 1748248492157, "duration": 451, "pid": 21388, "index": 77}, {"name": "Load Service", "start": 1748248491706, "end": 1748248491842, "duration": 136, "pid": 21388, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748248491707, "end": 1748248491842, "duration": 135, "pid": 21388, "index": 79}, {"name": "Load Middleware", "start": 1748248491842, "end": 1748248492001, "duration": 159, "pid": 21388, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748248491842, "end": 1748248491986, "duration": 144, "pid": 21388, "index": 81}, {"name": "Load Controller", "start": 1748248492001, "end": 1748248492051, "duration": 50, "pid": 21388, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748248492001, "end": 1748248492051, "duration": 50, "pid": 21388, "index": 83}, {"name": "Load Router", "start": 1748248492051, "end": 1748248492059, "duration": 8, "pid": 21388, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748248492052, "end": 1748248492053, "duration": 1, "pid": 21388, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748248492054, "end": 1748248492128, "duration": 74, "pid": 21388, "index": 86}]