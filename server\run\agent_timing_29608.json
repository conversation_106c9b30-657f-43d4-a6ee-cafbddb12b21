[{"name": "Process Start", "start": 1748253128734, "end": 1748253130409, "duration": 1675, "pid": 29608, "index": 0}, {"name": "Application Start", "start": 1748253130411, "end": 1748253131765, "duration": 1354, "pid": 29608, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748253130434, "end": 1748253130474, "duration": 40, "pid": 29608, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748253130474, "end": 1748253130515, "duration": 41, "pid": 29608, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748253130475, "end": 1748253130475, "duration": 0, "pid": 29608, "index": 4}, {"name": "Require(1) config/config.prod.js", "start": 1748253130477, "end": 1748253130478, "duration": 1, "pid": 29608, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748253130479, "end": 1748253130479, "duration": 0, "pid": 29608, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748253130480, "end": 1748253130480, "duration": 0, "pid": 29608, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748253130482, "end": 1748253130482, "duration": 0, "pid": 29608, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748253130483, "end": 1748253130483, "duration": 0, "pid": 29608, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748253130484, "end": 1748253130485, "duration": 1, "pid": 29608, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748253130486, "end": 1748253130486, "duration": 0, "pid": 29608, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748253130487, "end": 1748253130487, "duration": 0, "pid": 29608, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748253130488, "end": 1748253130489, "duration": 1, "pid": 29608, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1748253130490, "end": 1748253130490, "duration": 0, "pid": 29608, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1748253130491, "end": 1748253130492, "duration": 1, "pid": 29608, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1748253130493, "end": 1748253130493, "duration": 0, "pid": 29608, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1748253130494, "end": 1748253130495, "duration": 1, "pid": 29608, "index": 17}, {"name": "Require(14) node_modules/egg-jwt/config/config.default.js", "start": 1748253130496, "end": 1748253130496, "duration": 0, "pid": 29608, "index": 18}, {"name": "Require(15) node_modules/egg-cors/config/config.default.js", "start": 1748253130497, "end": 1748253130497, "duration": 0, "pid": 29608, "index": 19}, {"name": "Require(16) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748253130498, "end": 1748253130498, "duration": 0, "pid": 29608, "index": 20}, {"name": "Require(17) node_modules/egg-mysql/config/config.default.js", "start": 1748253130499, "end": 1748253130499, "duration": 0, "pid": 29608, "index": 21}, {"name": "Require(18) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748253130500, "end": 1748253130501, "duration": 1, "pid": 29608, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748253130502, "end": 1748253130503, "duration": 1, "pid": 29608, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748253130506, "end": 1748253130506, "duration": 0, "pid": 29608, "index": 24}, {"name": "Require(21) node_modules/egg-static/config/config.prod.js", "start": 1748253130511, "end": 1748253130511, "duration": 0, "pid": 29608, "index": 25}, {"name": "Require(22) config/config.prod.js", "start": 1748253130515, "end": 1748253130515, "duration": 0, "pid": 29608, "index": 26}, {"name": "Load extend/agent.js", "start": 1748253130516, "end": 1748253130607, "duration": 91, "pid": 29608, "index": 27}, {"name": "Require(23) node_modules/egg-security/app/extend/agent.js", "start": 1748253130517, "end": 1748253130519, "duration": 2, "pid": 29608, "index": 28}, {"name": "Require(24) node_modules/egg-schedule/app/extend/agent.js", "start": 1748253130521, "end": 1748253130594, "duration": 73, "pid": 29608, "index": 29}, {"name": "Require(25) node_modules/egg-logrotator/app/extend/agent.js", "start": 1748253130595, "end": 1748253130597, "duration": 2, "pid": 29608, "index": 30}, {"name": "Load extend/context.js", "start": 1748253130607, "end": 1748253130681, "duration": 74, "pid": 29608, "index": 31}, {"name": "Require(26) node_modules/egg-security/app/extend/context.js", "start": 1748253130608, "end": 1748253130626, "duration": 18, "pid": 29608, "index": 32}, {"name": "Require(27) node_modules/egg-jsonp/app/extend/context.js", "start": 1748253130627, "end": 1748253130631, "duration": 4, "pid": 29608, "index": 33}, {"name": "Require(28) node_modules/egg-i18n/app/extend/context.js", "start": 1748253130632, "end": 1748253130633, "duration": 1, "pid": 29608, "index": 34}, {"name": "Require(29) node_modules/egg-multipart/app/extend/context.js", "start": 1748253130635, "end": 1748253130663, "duration": 28, "pid": 29608, "index": 35}, {"name": "Require(30) node_modules/egg-view/app/extend/context.js", "start": 1748253130665, "end": 1748253130667, "duration": 2, "pid": 29608, "index": 36}, {"name": "Require(31) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748253130670, "end": 1748253130670, "duration": 0, "pid": 29608, "index": 37}, {"name": "Require(32) node_modules/egg/app/extend/context.js", "start": 1748253130672, "end": 1748253130675, "duration": 3, "pid": 29608, "index": 38}, {"name": "Load agent.js", "start": 1748253130682, "end": 1748253130734, "duration": 52, "pid": 29608, "index": 39}, {"name": "Require(33) node_modules/egg-security/agent.js", "start": 1748253130682, "end": 1748253130683, "duration": 1, "pid": 29608, "index": 40}, {"name": "Require(34) node_modules/egg-onerror/agent.js", "start": 1748253130684, "end": 1748253130685, "duration": 1, "pid": 29608, "index": 41}, {"name": "Require(35) node_modules/egg-watcher/agent.js", "start": 1748253130686, "end": 1748253130702, "duration": 16, "pid": 29608, "index": 42}, {"name": "Require(36) node_modules/egg-schedule/agent.js", "start": 1748253130703, "end": 1748253130705, "duration": 2, "pid": 29608, "index": 43}, {"name": "Require(37) node_modules/egg-logrotator/agent.js", "start": 1748253130706, "end": 1748253130707, "duration": 1, "pid": 29608, "index": 44}, {"name": "Require(38) node_modules/egg-sequelize/agent.js", "start": 1748253130708, "end": 1748253130708, "duration": 0, "pid": 29608, "index": 45}, {"name": "Require(39) node_modules/egg-mysql/agent.js", "start": 1748253130710, "end": 1748253130727, "duration": 17, "pid": 29608, "index": 46}, {"name": "Require(40) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1748253130728, "end": 1748253130732, "duration": 4, "pid": 29608, "index": 47}, {"name": "Require(41) node_modules/egg/agent.js", "start": 1748253130733, "end": 1748253130733, "duration": 0, "pid": 29608, "index": 48}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748253130741, "end": 1748253131695, "duration": 954, "pid": 29608, "index": 49}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1748253130742, "end": 1748253131683, "duration": 941, "pid": 29608, "index": 50}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748253131426, "end": 1748253131537, "duration": 111, "pid": 29608, "index": 51}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748253131465, "end": 1748253131694, "duration": 229, "pid": 29608, "index": 52}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748253131578, "end": 1748253131764, "duration": 186, "pid": 29608, "index": 53}]