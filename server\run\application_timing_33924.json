[{"name": "Process Start", "start": 1748248961846, "end": 1748248964225, "duration": 2379, "pid": 33924, "index": 0}, {"name": "Application Start", "start": 1748248964227, "end": 1748248967725, "duration": 3498, "pid": 33924, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748248964272, "end": 1748248964538, "duration": 266, "pid": 33924, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748248964538, "end": 1748248964620, "duration": 82, "pid": 33924, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748248964540, "end": 1748248964541, "duration": 1, "pid": 33924, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748248964545, "end": 1748248964546, "duration": 1, "pid": 33924, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748248964548, "end": 1748248964551, "duration": 3, "pid": 33924, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748248964552, "end": 1748248964552, "duration": 0, "pid": 33924, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748248964554, "end": 1748248964555, "duration": 1, "pid": 33924, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748248964556, "end": 1748248964557, "duration": 1, "pid": 33924, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748248964558, "end": 1748248964559, "duration": 1, "pid": 33924, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748248964560, "end": 1748248964560, "duration": 0, "pid": 33924, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748248964561, "end": 1748248964562, "duration": 1, "pid": 33924, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748248964563, "end": 1748248964563, "duration": 0, "pid": 33924, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748248964564, "end": 1748248964564, "duration": 0, "pid": 33924, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748248964565, "end": 1748248964566, "duration": 1, "pid": 33924, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748248964567, "end": 1748248964567, "duration": 0, "pid": 33924, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748248964568, "end": 1748248964569, "duration": 1, "pid": 33924, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748248964570, "end": 1748248964570, "duration": 0, "pid": 33924, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748248964571, "end": 1748248964571, "duration": 0, "pid": 33924, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748248964572, "end": 1748248964572, "duration": 0, "pid": 33924, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748248964573, "end": 1748248964574, "duration": 1, "pid": 33924, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748248964575, "end": 1748248964575, "duration": 0, "pid": 33924, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748248964576, "end": 1748248964577, "duration": 1, "pid": 33924, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748248964579, "end": 1748248964581, "duration": 2, "pid": 33924, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748248964597, "end": 1748248964597, "duration": 0, "pid": 33924, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748248964602, "end": 1748248964602, "duration": 0, "pid": 33924, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748248964605, "end": 1748248964610, "duration": 5, "pid": 33924, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748248964614, "end": 1748248964614, "duration": 0, "pid": 33924, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748248964619, "end": 1748248964619, "duration": 0, "pid": 33924, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748248964619, "end": 1748248964619, "duration": 0, "pid": 33924, "index": 30}, {"name": "Load extend/application.js", "start": 1748248964621, "end": 1748248964747, "duration": 126, "pid": 33924, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748248964622, "end": 1748248964623, "duration": 1, "pid": 33924, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748248964624, "end": 1748248964626, "duration": 2, "pid": 33924, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748248964627, "end": 1748248964634, "duration": 7, "pid": 33924, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748248964636, "end": 1748248964648, "duration": 12, "pid": 33924, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748248964650, "end": 1748248964652, "duration": 2, "pid": 33924, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748248964653, "end": 1748248964658, "duration": 5, "pid": 33924, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748248964660, "end": 1748248964736, "duration": 76, "pid": 33924, "index": 38}, {"name": "Load extend/request.js", "start": 1748248964747, "end": 1748248964765, "duration": 18, "pid": 33924, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748248964754, "end": 1748248964757, "duration": 3, "pid": 33924, "index": 40}, {"name": "Load extend/response.js", "start": 1748248964765, "end": 1748248964784, "duration": 19, "pid": 33924, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748248964773, "end": 1748248964777, "duration": 4, "pid": 33924, "index": 42}, {"name": "Load extend/context.js", "start": 1748248964785, "end": 1748248964866, "duration": 81, "pid": 33924, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748248964786, "end": 1748248964808, "duration": 22, "pid": 33924, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748248964809, "end": 1748248964812, "duration": 3, "pid": 33924, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748248964813, "end": 1748248964813, "duration": 0, "pid": 33924, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748248964815, "end": 1748248964844, "duration": 29, "pid": 33924, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748248964845, "end": 1748248964848, "duration": 3, "pid": 33924, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748248964850, "end": 1748248964850, "duration": 0, "pid": 33924, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748248964851, "end": 1748248964854, "duration": 3, "pid": 33924, "index": 50}, {"name": "Load extend/helper.js", "start": 1748248964866, "end": 1748248964917, "duration": 51, "pid": 33924, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748248964867, "end": 1748248964897, "duration": 30, "pid": 33924, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748248964903, "end": 1748248964904, "duration": 1, "pid": 33924, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748248964905, "end": 1748248964906, "duration": 1, "pid": 33924, "index": 54}, {"name": "Load app.js", "start": 1748248964917, "end": 1748248965043, "duration": 126, "pid": 33924, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748248964918, "end": 1748248964919, "duration": 1, "pid": 33924, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748248964920, "end": 1748248964924, "duration": 4, "pid": 33924, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748248964927, "end": 1748248964947, "duration": 20, "pid": 33924, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748248964948, "end": 1748248964965, "duration": 17, "pid": 33924, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748248964966, "end": 1748248964985, "duration": 19, "pid": 33924, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748248964986, "end": 1748248964987, "duration": 1, "pid": 33924, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748248964988, "end": 1748248964992, "duration": 4, "pid": 33924, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748248964993, "end": 1748248964993, "duration": 0, "pid": 33924, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748248964994, "end": 1748248964995, "duration": 1, "pid": 33924, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748248964996, "end": 1748248964997, "duration": 1, "pid": 33924, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748248964999, "end": 1748248965000, "duration": 1, "pid": 33924, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748248965000, "end": 1748248965001, "duration": 1, "pid": 33924, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748248965001, "end": 1748248965002, "duration": 1, "pid": 33924, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748248965002, "end": 1748248965005, "duration": 3, "pid": 33924, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748248965005, "end": 1748248965031, "duration": 26, "pid": 33924, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748248965032, "end": 1748248965041, "duration": 9, "pid": 33924, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748248965063, "end": 1748248967697, "duration": 2634, "pid": 33924, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748248966097, "end": 1748248966193, "duration": 96, "pid": 33924, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748248966125, "end": 1748248967525, "duration": 1400, "pid": 33924, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748248966226, "end": 1748248967724, "duration": 1498, "pid": 33924, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748248966337, "end": 1748248967690, "duration": 1353, "pid": 33924, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748248966434, "end": 1748248967594, "duration": 1160, "pid": 33924, "index": 77}, {"name": "Load Service", "start": 1748248966434, "end": 1748248966566, "duration": 132, "pid": 33924, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748248966434, "end": 1748248966566, "duration": 132, "pid": 33924, "index": 79}, {"name": "Load Middleware", "start": 1748248966567, "end": 1748248967311, "duration": 744, "pid": 33924, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748248966567, "end": 1748248967280, "duration": 713, "pid": 33924, "index": 81}, {"name": "Load Controller", "start": 1748248967311, "end": 1748248967409, "duration": 98, "pid": 33924, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748248967311, "end": 1748248967409, "duration": 98, "pid": 33924, "index": 83}, {"name": "Load Router", "start": 1748248967409, "end": 1748248967419, "duration": 10, "pid": 33924, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748248967410, "end": 1748248967411, "duration": 1, "pid": 33924, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748248967413, "end": 1748248967525, "duration": 112, "pid": 33924, "index": 86}]