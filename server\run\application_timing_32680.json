[{"name": "Process Start", "start": 1748254686756, "end": 1748254689298, "duration": 2542, "pid": 32680, "index": 0}, {"name": "Application Start", "start": 1748254689301, "end": 1748254692099, "duration": 2798, "pid": 32680, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748254689329, "end": 1748254689488, "duration": 159, "pid": 32680, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748254689488, "end": 1748254689553, "duration": 65, "pid": 32680, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748254689490, "end": 1748254689490, "duration": 0, "pid": 32680, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748254689494, "end": 1748254689495, "duration": 1, "pid": 32680, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748254689496, "end": 1748254689496, "duration": 0, "pid": 32680, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748254689498, "end": 1748254689498, "duration": 0, "pid": 32680, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748254689501, "end": 1748254689501, "duration": 0, "pid": 32680, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748254689502, "end": 1748254689503, "duration": 1, "pid": 32680, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748254689504, "end": 1748254689504, "duration": 0, "pid": 32680, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748254689505, "end": 1748254689506, "duration": 1, "pid": 32680, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748254689507, "end": 1748254689507, "duration": 0, "pid": 32680, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748254689509, "end": 1748254689510, "duration": 1, "pid": 32680, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748254689511, "end": 1748254689512, "duration": 1, "pid": 32680, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748254689514, "end": 1748254689515, "duration": 1, "pid": 32680, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748254689517, "end": 1748254689518, "duration": 1, "pid": 32680, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748254689519, "end": 1748254689520, "duration": 1, "pid": 32680, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748254689522, "end": 1748254689522, "duration": 0, "pid": 32680, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748254689524, "end": 1748254689525, "duration": 1, "pid": 32680, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748254689526, "end": 1748254689527, "duration": 1, "pid": 32680, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748254689528, "end": 1748254689528, "duration": 0, "pid": 32680, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748254689529, "end": 1748254689530, "duration": 1, "pid": 32680, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748254689531, "end": 1748254689532, "duration": 1, "pid": 32680, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748254689533, "end": 1748254689535, "duration": 2, "pid": 32680, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748254689537, "end": 1748254689537, "duration": 0, "pid": 32680, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748254689539, "end": 1748254689540, "duration": 1, "pid": 32680, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748254689542, "end": 1748254689542, "duration": 0, "pid": 32680, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748254689546, "end": 1748254689546, "duration": 0, "pid": 32680, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748254689552, "end": 1748254689553, "duration": 1, "pid": 32680, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748254689553, "end": 1748254689553, "duration": 0, "pid": 32680, "index": 30}, {"name": "Load extend/application.js", "start": 1748254689555, "end": 1748254689705, "duration": 150, "pid": 32680, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748254689557, "end": 1748254689558, "duration": 1, "pid": 32680, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748254689559, "end": 1748254689562, "duration": 3, "pid": 32680, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748254689563, "end": 1748254689575, "duration": 12, "pid": 32680, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748254689577, "end": 1748254689586, "duration": 9, "pid": 32680, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748254689587, "end": 1748254689590, "duration": 3, "pid": 32680, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748254689591, "end": 1748254689593, "duration": 2, "pid": 32680, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748254689595, "end": 1748254689691, "duration": 96, "pid": 32680, "index": 38}, {"name": "Load extend/request.js", "start": 1748254689705, "end": 1748254689730, "duration": 25, "pid": 32680, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748254689717, "end": 1748254689719, "duration": 2, "pid": 32680, "index": 40}, {"name": "Load extend/response.js", "start": 1748254689730, "end": 1748254689754, "duration": 24, "pid": 32680, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748254689740, "end": 1748254689746, "duration": 6, "pid": 32680, "index": 42}, {"name": "Load extend/context.js", "start": 1748254689754, "end": 1748254689849, "duration": 95, "pid": 32680, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748254689755, "end": 1748254689778, "duration": 23, "pid": 32680, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748254689779, "end": 1748254689782, "duration": 3, "pid": 32680, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748254689783, "end": 1748254689784, "duration": 1, "pid": 32680, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748254689786, "end": 1748254689826, "duration": 40, "pid": 32680, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748254689828, "end": 1748254689830, "duration": 2, "pid": 32680, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748254689833, "end": 1748254689835, "duration": 2, "pid": 32680, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748254689836, "end": 1748254689840, "duration": 4, "pid": 32680, "index": 50}, {"name": "Load extend/helper.js", "start": 1748254689849, "end": 1748254689922, "duration": 73, "pid": 32680, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748254689851, "end": 1748254689893, "duration": 42, "pid": 32680, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748254689903, "end": 1748254689904, "duration": 1, "pid": 32680, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748254689906, "end": 1748254689907, "duration": 1, "pid": 32680, "index": 54}, {"name": "Load app.js", "start": 1748254689923, "end": 1748254690045, "duration": 122, "pid": 32680, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748254689924, "end": 1748254689925, "duration": 1, "pid": 32680, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748254689925, "end": 1748254689930, "duration": 5, "pid": 32680, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748254689933, "end": 1748254689957, "duration": 24, "pid": 32680, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748254689958, "end": 1748254689976, "duration": 18, "pid": 32680, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748254689977, "end": 1748254689995, "duration": 18, "pid": 32680, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748254689995, "end": 1748254689996, "duration": 1, "pid": 32680, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748254689997, "end": 1748254690004, "duration": 7, "pid": 32680, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748254690004, "end": 1748254690005, "duration": 1, "pid": 32680, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748254690006, "end": 1748254690006, "duration": 0, "pid": 32680, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748254690007, "end": 1748254690007, "duration": 0, "pid": 32680, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748254690008, "end": 1748254690009, "duration": 1, "pid": 32680, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748254690009, "end": 1748254690010, "duration": 1, "pid": 32680, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748254690010, "end": 1748254690011, "duration": 1, "pid": 32680, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748254690011, "end": 1748254690014, "duration": 3, "pid": 32680, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748254690015, "end": 1748254690036, "duration": 21, "pid": 32680, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748254690038, "end": 1748254690043, "duration": 5, "pid": 32680, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748254690062, "end": 1748254692077, "duration": 2015, "pid": 32680, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748254690817, "end": 1748254690942, "duration": 125, "pid": 32680, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748254690846, "end": 1748254692012, "duration": 1166, "pid": 32680, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748254690993, "end": 1748254692099, "duration": 1106, "pid": 32680, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748254691133, "end": 1748254692078, "duration": 945, "pid": 32680, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748254691260, "end": 1748254692037, "duration": 777, "pid": 32680, "index": 77}, {"name": "Load Service", "start": 1748254691260, "end": 1748254691495, "duration": 235, "pid": 32680, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748254691261, "end": 1748254691495, "duration": 234, "pid": 32680, "index": 79}, {"name": "Load Middleware", "start": 1748254691495, "end": 1748254691822, "duration": 327, "pid": 32680, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748254691495, "end": 1748254691777, "duration": 282, "pid": 32680, "index": 81}, {"name": "Load Controller", "start": 1748254691822, "end": 1748254691884, "duration": 62, "pid": 32680, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748254691822, "end": 1748254691883, "duration": 61, "pid": 32680, "index": 83}, {"name": "Load Router", "start": 1748254691884, "end": 1748254691893, "duration": 9, "pid": 32680, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748254691884, "end": 1748254691885, "duration": 1, "pid": 32680, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748254691886, "end": 1748254692012, "duration": 126, "pid": 32680, "index": 86}]